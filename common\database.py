import psycopg2
from config import Config


def get_qbook_db_config():
    """Get QBook database configuration from Config."""
    return {
        'name': Config.QBOOK_USERNAME,
        'password': Config.QBOOK_PASSWORD,
        'host': Config.QBOOK_HOST,
        'db_name': Config.QBOOK_DATABASE,
        'port': Config.QBOOK_PORT
    }


def connect_database(db_data):
    # if db_data['host'] != '********':
    #     db_data['host'] = '***********'
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection


# CREATE TABLE public.tgt_statements (
# 	id bigserial NOT NULL,
# 	tgt_id int8 NOT NULL,
# 	statement_number int8 NOT NULL,
# 	target_statement text NULL,
# 	converted_statement text NULL,
# 	source_statement text NULL,
# 	created_dt timestamp DEFAULT now() NULL,
# 	updated_dt timestamp NULL,
# 	CONSTRAINT tgt_statements_pkey PRIMARY KEY (id),
# 	CONSTRAINT tgt_statements_unique UNIQUE (tgt_id, statement_number)
# );


# CREATE TABLE public.conversion_agent_deployment (
# 	deployment_id bigserial NOT NULL,
# 	tgt_statements_id int8 NOT NULL,
# 	attempt int8 NOT NULL,
# 	source_statement text NULL,
# 	target_statement text NULL,
# 	converted_statement text NULL,
# 	observations text NULL,
# 	is_deployed bool DEFAULT false NULL,
# 	error text NULL,
# 	review_status bool DEFAULT false NULL,
# 	reviewer_comments text NULL,
# 	reviewer_name text NULL,
# 	created_dt timestamp DEFAULT now() NULL,
# 	updated_dt timestamp NULL,
# 	CONSTRAINT conversion_agent_deployment_pkey PRIMARY KEY (deployment_id),
# 	CONSTRAINT deployment_unique UNIQUE (tgt_statements_id, attempt)
# );


# -- public.conversion_agent_deployment foreign keys

# ALTER TABLE public.conversion_agent_deployment ADD CONSTRAINT fk_tgt_statements FOREIGN KEY (tgt_statements_id) REFERENCES public.tgt_statements(id) ON DELETE CASCADE;

# INSERT INTO public.tgt_statements (tgt_id, statement_number,target_statement,
#     converted_statement,
#     source_statement,
#     created_dt
# )
# VALUES (
# tgt_id,
#     statement_number,
#     target_statement,
#     converted_statement,
#     source_statement,

#     now()
# )
# ON CONFLICT (tgt_id, statement_number)
# DO UPDATE SET
#     target_statement = EXCLUDED.target_statement,
#     converted_statement = EXCLUDED.converted_statement,
#     source_statement = EXCLUDED.source_statement,
#     updated_dt = now();

# public.sp_tgt_statements_insert(IN p_tgt_id bigint, IN p_statement_number bigint, IN p_target_statement text, IN p_converted_statement text, IN p_source_statement text, INOUT ref refcursor)
# public.sp_conversion_agent_deployment_insert(IN p_tgt_id bigint,p_statement_number bigint, IN p_attempt bigint,IN p_source_statement text, IN p_target_statement text, IN p_converted_statement text,  IN p_observations text, IN p_is_deployed boolean, IN p_error text, INOUT ref refcursor)


def target_statements_batch_insert(connection, tgt_id, statements_data):
    """
    Batch insert function for target statements using direct INSERT with ON CONFLICT.

    Args:
        connection: Database connection object
        tgt_id: Target ID
        statements_data: List of tuples containing (statement_number, target_statement)

    Returns:
        bool: True if successful, False otherwise
    """
    cursor = connection.cursor()
    success = True

    try:
        # First, delete existing records for this tgt_id
        delete_sql = "DELETE FROM public.tgt_statements WHERE tgt_id = %s"
        cursor.execute(delete_sql, (int(tgt_id),))
        print(f'Deleted existing records for tgt_id: {tgt_id}')

        # Prepare batch data for direct INSERT
        batch_data = []
        for statement_number, target_statement in statements_data:
            batch_data.append(
                (int(tgt_id), int(statement_number), target_statement, None, None))

        # Direct INSERT statement (no ON CONFLICT needed since we deleted first)
        insert_sql = """
        INSERT INTO public.tgt_statements (
            tgt_id,
            statement_number,
            target_statement,
            converted_statement,
            source_statement,
            created_dt
        )
        VALUES (%s, %s, %s, %s, %s, now())
        """

        cursor.executemany(insert_sql, batch_data)
        connection.commit()
        print(
            f'Target statements batch insert executed successfully - {len(statements_data)} records processed')

    except Exception as error:
        success = False
        connection.rollback()
        print("Error at target statements batch insert: ", str(error))
    finally:
        cursor.close()
    return success


def conversion_agent_deployment_insert(connection, tgt_id, statement_number, source_statement_number, attempt, source_statement, target_statement, converted_statement, observations, is_deployed, error):
    """
    Insert conversion agent deployment record using stored procedure.

    Args:
        connection: Database connection object
        tgt_id: Target ID
        statement_number: Statement number
        attempt: Attempt number
        source_statement: Source statement text
        source_statement_number: Source statement number
        target_statement: Target statement text
        converted_statement: Converted statement text
        observations: Observations text
        is_deployed: Boolean indicating if deployed
        error: Error text

    Returns:
        bool: True if successful, False otherwise
    """
    cursor = connection.cursor()
    success = True

    try:
        # Call stored procedure for conversion agent deployment insert
        sp_call = 'call public.sp_conversion_agent_deployment_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "refcursor"'
        # print(sp_call, (int(tgt_id), int(statement_number), int(attempt), source_statement, target_statement, converted_statement, observations, is_deployed, error, 'refcursor'), 'conversion_agent_deployment_insert')
        cursor.execute(sp_call, (int(tgt_id), int(statement_number), int(attempt), source_statement,
                       source_statement_number, target_statement, converted_statement, observations, is_deployed, error, 'refcursor'))
        connection.commit()
        print(
            f'Conversion agent deployment insert executed successfully - tgt_id: {tgt_id}, statement_number: {statement_number}, attempt: {attempt}')

    except Exception as error:
        success = False
        connection.rollback()
        print("Error at conversion agent deployment insert: ", str(error))
    finally:
        cursor.close()
    return success


def request_insert(connection, iteration_id, connection_id, operation_name, operation_category, schema_name,
                   object_type):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.request_insert(%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset"',
                       (iteration_id, connection_id, operation_name, operation_category, schema_name, object_type,
                        'dataset'))
        data = cursor.fetchall()
        print("Request insert executed successfully")
    except Exception as error:
        data = None
        print("Error at request insert: ", str(error))
    finally:
        connection.commit()
        cursor.close()
    return data


def request_update(connection, request_id, status, error):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.request_update(%s,%s,%s,%s);fetch all in "dataset";',
                       (request_id, status, error, 'dataset'))
        print("Request update executed successfully")
    except Exception as err:
        print("Error at request update: ", str(err))
    finally:
        connection.commit()
        cursor.close()


def get_approved_statements_from_db(connection, tgt_object_id):
    """
    Retrieve approved statements from database where review_status and is_manually_deployed are both true.

    Args:
        tgt_object_id (int): The target object ID

    Returns:
        list: List of approved statements data
    """
    try:
        cursor = connection.cursor()

        query = """
        SELECT
            cad.source_statement_number,
            ts.statement_number as target_statement_id,
            cad.source_statement,
            cad.target_statement,
            cad.converted_statement as ai_converted_statement,
            cad.error as deployment_error
        FROM public.conversion_agent_deployment cad
        INNER JOIN public.tgt_statements ts ON cad.tgt_statements_id = ts.id
        WHERE cad.review_status = true
            AND cad.is_manually_deployed = true
            AND ts.tgt_id = %s
        ORDER BY cad.created_dt
        """

        cursor.execute(query, (tgt_object_id,))
        results = cursor.fetchall()

        approved_statements = []
        for result in results:
            approved_statements.append({
                'source_statement_number': result[0],
                'target_statement_id': result[1],
                'source_statement': result[2],
                'target_statement': result[3],
                'ai_converted_statement': result[4],
                'deployment_error': result[5]
            })

        print(
            f"📊 Retrieved {len(approved_statements)} approved statements from database for tgt_object_id: {tgt_object_id}")
        return approved_statements

    except Exception as e:
        print(f"❌ Error retrieving approved statements: {str(e)}")
        return []
    finally:
        if 'cursor' in locals():
            cursor.close()


# ========================================Stage2==============================================

# CREATE TABLE IF NOT EXISTS public.stage2_statements (
#     statement_id BIGSERIAL PRIMARY KEY,
#     process_type TEXT,
#     tgt_object_id BIGINT,
#     schema_name VARCHAR(50),
#     tgt_object_name VARCHAR(100),
#     migration_name VARCHAR(100),
#     object_type VARCHAR(100),
#     original_error TEXT,
#     target_statement_number BIGINT,
#     source_statement_number BIGINT,
#     original_source_statement TEXT,
#     source_before_statement_level TEXT,
#     source_after_statement_level TEXT,
#     target_statement TEXT,
#     ai_converted_statement TEXT,
#     statement_level_features TEXT,
#     program_level_features TEXT,
#     responsible_statement_level_features TEXT,
#     responsible_program_level_features TEXT,
#     created_dt TIMESTAMP DEFAULT NOW(),
#     updated_dt TIMESTAMP NULL
# );

# -- Stored procedure for initial metadata insert (Stage 1 CSV generation)
# CREATE OR REPLACE FUNCTION sp_stage2_statement_metadata_insert(
#     p_process_type TEXT,
#     p_tgt_object_id BIGINT,
#     p_schema_name VARCHAR(50),
#     p_tgt_object_name VARCHAR(100),
#     p_migration_name VARCHAR(100),
#     p_object_type VARCHAR(100),
#     p_original_error TEXT,
#     p_target_statement_number BIGINT,
#     p_source_statement_number BIGINT,
#     p_original_source_statement TEXT,
#     p_target_statement TEXT,
#     p_ai_converted_statement TEXT
# )
# RETURNS BIGINT
# LANGUAGE plpgsql
# AS $$
# DECLARE
#     v_statement_id BIGINT;
#     v_existing_id BIGINT;
# BEGIN
#     BEGIN
#         -- Check if record already exists based on business keys
#         SELECT statement_id INTO v_existing_id
#         FROM public.stage2_statements
#         WHERE TRIM(LOWER(process_type)) = TRIM(LOWER(p_process_type))
#           AND TRIM(LOWER(migration_name)) = TRIM(LOWER(p_migration_name))
#           AND TRIM(LOWER(object_type)) = TRIM(LOWER(p_object_type))
#           AND TRIM(LOWER(tgt_object_name)) = TRIM(LOWER(p_tgt_object_name))
#           AND TRIM(LOWER(schema_name)) = TRIM(LOWER(p_schema_name))
#           AND target_statement_number = p_target_statement_number;

#         IF v_existing_id IS NOT NULL THEN
#             -- Update existing record with metadata
#             UPDATE public.stage2_statements
#             SET tgt_object_id = p_tgt_object_id,
#                 original_error = p_original_error,
#                 source_statement_number = p_source_statement_number,
#                 original_source_statement = p_original_source_statement,
#                 target_statement = p_target_statement,
#                 ai_converted_statement = p_ai_converted_statement,
#                 updated_dt = NOW()
#             WHERE statement_id = v_existing_id;

#             v_statement_id := v_existing_id;
#         ELSE
#             -- Insert new record with metadata only (no updated_dt on insert)
#             INSERT INTO public.stage2_statements (
#                 process_type, tgt_object_id, schema_name, tgt_object_name, migration_name,
#                 object_type, original_error, target_statement_number, source_statement_number,
#                 original_source_statement, target_statement, ai_converted_statement,
#                 created_dt
#             ) VALUES (
#                 p_process_type, p_tgt_object_id, p_schema_name, p_tgt_object_name, p_migration_name,
#                 p_object_type, p_original_error, p_target_statement_number, p_source_statement_number,
#                 p_original_source_statement, p_target_statement, p_ai_converted_statement,
#                 NOW()
#             ) RETURNING statement_id INTO v_statement_id;
#         END IF;

#         RETURN v_statement_id;

#     EXCEPTION
#         WHEN OTHERS THEN
#             RAISE EXCEPTION 'Error in sp_stage2_statement_metadata_insert: %', SQLERRM;
#             RETURN NULL;
#     END;
# END;
# $$;

# -- Stored procedure for features update (identify_responsible_features node)
# CREATE OR REPLACE FUNCTION sp_stage2_statement_features_update(
#     p_process_type TEXT,
#     p_migration_name VARCHAR(100),
#     p_schema_name VARCHAR(50),
#     p_object_type VARCHAR(100),
#     p_tgt_object_name VARCHAR(100),
#     p_target_statement_number BIGINT,
#     p_source_before_statement_level TEXT,
#     p_source_after_statement_level TEXT,
#     p_statement_level_features TEXT,
#     p_program_level_features TEXT,
#     p_responsible_statement_level_features TEXT,
#     p_responsible_program_level_features TEXT
# )
# RETURNS BIGINT
# LANGUAGE plpgsql
# AS $$
# DECLARE
#     v_statement_id BIGINT;
# BEGIN
#     BEGIN
#         -- Update existing record with features data
#         UPDATE public.stage2_statements
#         SET source_before_statement_level = p_source_before_statement_level,
#             source_after_statement_level = p_source_after_statement_level,
#             statement_level_features = p_statement_level_features,
#             program_level_features = p_program_level_features,
#             responsible_statement_level_features = p_responsible_statement_level_features,
#             responsible_program_level_features = p_responsible_program_level_features,
#             updated_dt = NOW()
#         WHERE TRIM(LOWER(process_type)) = TRIM(LOWER(p_process_type))
#           AND TRIM(LOWER(migration_name)) = TRIM(LOWER(p_migration_name))
#           AND TRIM(LOWER(object_type)) = TRIM(LOWER(p_object_type))
#           AND TRIM(LOWER(tgt_object_name)) = TRIM(LOWER(p_tgt_object_name))
#           AND TRIM(LOWER(schema_name)) = TRIM(LOWER(p_schema_name))
#           AND target_statement_number = p_target_statement_number
#         RETURNING statement_id INTO v_statement_id;

#         IF NOT FOUND THEN
#             RAISE EXCEPTION 'No statement found for update with process_type=%, migration_name=%, schema_name=%, object_type=%, object_name=%, target_statement_number=%',
#                 p_process_type, p_migration_name, p_schema_name, p_object_type, p_tgt_object_name, p_target_statement_number;
#         END IF;

#         RETURN v_statement_id;

#     EXCEPTION
#         WHEN OTHERS THEN
#             RAISE EXCEPTION 'Error in sp_stage2_statement_features_update: %', SQLERRM;
#             RETURN NULL;
#     END;
# END;
# $$;


# -- public.stage2_conversion_modules definition

# -- Drop table

# -- DROP TABLE public.stage2_conversion_modules;

# CREATE TABLE public.stage2_conversion_modules (
# 	id bigserial NOT NULL,
# 	statement_id int8 NOT NULL,
# 	feature_name varchar(255) NULL,
# 	original_object_path text NULL,
# 	updated_object_path text NULL,
# 	is_inregistry bool DEFAULT false NULL,
# 	registry_path text NULL,
# 	attempt int4 NULL,
# 	original_module text NULL,
# 	updated_module text NULL,
# 	registry_module_code text NULL,
# 	is_reviewed bool DEFAULT false NULL,
# 	reviewer_comments text NULL,
# 	reviewer_name text NULL,
# 	is_merged bool DEFAULT false NULL,
# 	created_dt timestamp DEFAULT now() NULL,
# 	updated_dt timestamp NULL,
# 	CONSTRAINT stage2_conversion_modules_pkey PRIMARY KEY (id),
# 	CONSTRAINT fk_stage2_modules_statement FOREIGN KEY (statement_id) REFERENCES public.stage2_statements(statement_id)
# );


# CREATE OR REPLACE FUNCTION sp_insert_stage2_conversion_module(
#     p_statement_id BIGINT,
#     p_feature_name VARCHAR(255),
#     p_original_object_path TEXT,  -- Original QMigrator module path (e.g., Common/Statement/Pre/join.py)
#     p_updated_object_path TEXT,   -- Enhanced decomposed module path (e.g., Stage1_Metadata/.../join_attempt_1.py)
#     p_attempt INTEGER,
#     p_is_inregistry BOOLEAN,
#     p_registry_path TEXT,
#     p_original_module TEXT,
#     p_updated_module TEXT,
#     p_registry_module_code TEXT DEFAULT NULL  -- Enhanced module code from registry
# )
# RETURNS BIGINT
# LANGUAGE plpgsql
# AS $$
# DECLARE
#     v_module_id BIGINT;
#     v_existing_id BIGINT;
# BEGIN
#     BEGIN
#         -- Check if record already exists based on statement_id + feature_name + original_object_path + attempt (case-insensitive, trimmed)
#         SELECT id INTO v_existing_id
#         FROM public.stage2_conversion_modules
#         WHERE statement_id = p_statement_id
#           AND TRIM(LOWER(feature_name)) = TRIM(LOWER(p_feature_name))
#           AND TRIM(LOWER(original_object_path)) = TRIM(LOWER(p_original_object_path))
#           AND attempt = p_attempt;

#         IF v_existing_id IS NOT NULL THEN
#             -- UPDATE existing record
#             UPDATE public.stage2_conversion_modules
#             SET
#                 updated_object_path = p_updated_object_path,
#                 is_inregistry = p_is_inregistry,
#                 registry_path = p_registry_path,
#                 original_module = p_original_module,
#                 updated_module = p_updated_module,
#                 registry_module_code = p_registry_module_code,
#                 updated_dt = NOW()
#             WHERE id = v_existing_id;

#             v_module_id := v_existing_id;
#         ELSE
#             -- INSERT new record
#             INSERT INTO public.stage2_conversion_modules (
#                 statement_id,
#                 feature_name,
#                 original_object_path,
#                 updated_object_path,
#                 attempt,
#                 is_inregistry,
#                 registry_path,
#                 original_module,
#                 updated_module,
#                 registry_module_code,
#                 created_dt
#             ) VALUES (
#                 p_statement_id,
#                 p_feature_name,
#                 p_original_object_path,
#                 p_updated_object_path,
#                 p_attempt,
#                 p_is_inregistry,
#                 p_registry_path,
#                 p_original_module,
#                 p_updated_module,
#                 p_registry_module_code,
#                 NOW()
#             ) RETURNING id INTO v_module_id;
#         END IF;

#         RETURN v_module_id;

#     EXCEPTION
#         WHEN OTHERS THEN
#             RAISE EXCEPTION 'Error inserting/updating stage2_conversion_module: %', SQLERRM;
#             RETURN NULL;
#     END;
# END;
# $$;


# CREATE OR REPLACE FUNCTION sp_update_stage2_conversion_module_status(
#     p_module_id BIGINT,
#     p_is_reviewed BOOLEAN DEFAULT NULL,
#     p_reviewer_comments TEXT DEFAULT NULL,
#     p_reviewer_name VARCHAR(255) DEFAULT NULL,
#     p_is_merged BOOLEAN DEFAULT NULL,
#     p_updated_module TEXT DEFAULT NULL,
#     p_registry_module_code TEXT DEFAULT NULL,
#     p_is_inregistry BOOLEAN DEFAULT NULL,
#     p_registry_path TEXT DEFAULT NULL
# )
# RETURNS BIGINT
# LANGUAGE plpgsql
# AS $$
# DECLARE
#     v_rows_affected INTEGER;
# BEGIN
#     BEGIN
#         UPDATE public.stage2_conversion_modules
#         SET
#             is_reviewed = COALESCE(p_is_reviewed, is_reviewed),
#             reviewer_comments = COALESCE(p_reviewer_comments, reviewer_comments),
#             reviewer_name = COALESCE(p_reviewer_name, reviewer_name),
#             is_merged = COALESCE(p_is_merged, is_merged),
#             updated_module = COALESCE(p_updated_module, updated_module),
#             registry_module_code = COALESCE(p_registry_module_code, registry_module_code),
#             is_inregistry = COALESCE(p_is_inregistry, is_inregistry),
#             registry_path = COALESCE(p_registry_path, registry_path),
#             updated_dt = NOW()
#         WHERE id = p_module_id;

#         GET DIAGNOSTICS v_rows_affected = ROW_COUNT;

#         IF v_rows_affected = 0 THEN
#             RAISE EXCEPTION 'No conversion module found with id: %', p_module_id;
#         END IF;

#         RETURN p_module_id;

#     EXCEPTION
#         WHEN OTHERS THEN
#             RAISE EXCEPTION 'Error updating stage2_conversion_module status for id %: %', p_module_id, SQLERRM;
#             RETURN NULL;
#     END;
# END;
# $$;


# CREATE TABLE public.requests (
#     request_id serial4 NOT NULL, -- primary key
#     process_type text NULL,
#     migration_name text NULL,
#     schema_name text NULL,
#     object_type text NULL,
#     object_name text NULL,
#     status text NULL,  -- Started/Completed/Error
#     error text NULL,
#     created_dt timestamp DEFAULT now() NULL,
#     updated_dt timestamp DEFAULT now() NULL,
#     CONSTRAINT requests_pkey PRIMARY KEY (request_id)
# );

# CREATE OR REPLACE FUNCTION sp_insert_request(
#     p_process_type TEXT,
#     p_migration_name TEXT,
#     p_schema_name TEXT,
#     p_object_type TEXT,
#     p_object_name TEXT,
#     p_status TEXT DEFAULT 'Started'
# )
# RETURNS BIGINT
# LANGUAGE plpgsql
# AS $$
# DECLARE
#     v_request_id BIGINT;
#     v_existing_id BIGINT;
# BEGIN
#     BEGIN
#         -- Check if record already exists based on case-insensitive, trimmed business keys
#         SELECT request_id INTO v_existing_id
#         FROM public.requests
#         WHERE TRIM(LOWER(process_type)) = TRIM(LOWER(p_process_type))
#           AND TRIM(LOWER(migration_name)) = TRIM(LOWER(p_migration_name))
#           AND TRIM(LOWER(schema_name)) = TRIM(LOWER(p_schema_name))
#           AND TRIM(LOWER(object_type)) = TRIM(LOWER(p_object_type))
#           AND TRIM(LOWER(object_name)) = TRIM(LOWER(p_object_name));

#         IF v_existing_id IS NOT NULL THEN
#             -- Record exists, update status and timestamp
#             UPDATE public.requests
#             SET status = p_status,
#                 updated_dt = NOW(),
#                 error = NULL  -- Clear any previous error
#             WHERE request_id = v_existing_id;

#             v_request_id := v_existing_id;
#         ELSE
#             -- INSERT new record
#             INSERT INTO public.requests (
#                 process_type,
#                 migration_name,
#                 schema_name,
#                 object_type,
#                 object_name,
#                 status,
#                 created_dt,
#                 updated_dt
#             ) VALUES (
#                 p_process_type,
#                 p_migration_name,
#                 p_schema_name,
#                 p_object_type,
#                 p_object_name,
#                 p_status,
#                 NOW(),
#                 NOW()
#             ) RETURNING request_id INTO v_request_id;
#         END IF;

#         RETURN v_request_id;

#     EXCEPTION
#         WHEN OTHERS THEN
#             RAISE EXCEPTION 'Error in sp_insert_request: %', SQLERRM;
#             RETURN NULL;
#     END;
# END;
# $$;


# CREATE OR REPLACE FUNCTION sp_update_request_by_id(
#     p_request_id BIGINT,
#     p_status TEXT,
#     p_error TEXT DEFAULT NULL
# )
# RETURNS BIGINT
# LANGUAGE plpgsql
# AS $$
# DECLARE
#     v_rows_affected INTEGER;
# BEGIN
#     BEGIN
#         -- Update record by request_id directly
#         UPDATE public.requests
#         SET status = p_status,
#             error = p_error,
#             updated_dt = NOW()
#         WHERE request_id = p_request_id;

#         GET DIAGNOSTICS v_rows_affected = ROW_COUNT;

#         IF v_rows_affected = 0 THEN
#             RAISE EXCEPTION 'No request found with request_id=%', p_request_id;
#             RETURN NULL;
#         END IF;

#         RETURN p_request_id;

#     EXCEPTION
#         WHEN OTHERS THEN
#             RAISE EXCEPTION 'Error in sp_update_request_by_id: %', SQLERRM;
#             RETURN NULL;
#     END;
# END;
# $$;


# CREATE TABLE public.stage2_statement_attempts (
#     attempt_id BIGSERIAL PRIMARY KEY,
#     statement_id BIGINT NOT NULL,
#     attempt_number INTEGER NOT NULL,
#     stage2_output_statement TEXT,
#     ai_comparison_status VARCHAR(50),  -- 'Success', 'Failed' (based on AI comparison result)
#     created_dt TIMESTAMP DEFAULT NOW(),
#     updated_dt TIMESTAMP NULL,

#     CONSTRAINT uk_statement_attempt UNIQUE (statement_id, attempt_number),
#     CONSTRAINT fk_statement_attempts_statement_id
#         FOREIGN KEY (statement_id) REFERENCES stage2_statements(statement_id)
# );

# CREATE OR REPLACE FUNCTION sp_insert_update_statement_attempt(
#     p_statement_id BIGINT,
#     p_attempt_number INTEGER,
#     p_stage2_output_statement TEXT,
#     p_ai_comparison_status VARCHAR(50)
# )
# RETURNS BIGINT
# LANGUAGE plpgsql
# AS $$
# DECLARE
#     v_attempt_id BIGINT;
#     v_existing_id BIGINT;
# BEGIN
#     BEGIN
#         -- Check if record already exists
#         SELECT attempt_id INTO v_existing_id
#         FROM public.stage2_statement_attempts
#         WHERE statement_id = p_statement_id
#           AND attempt_number = p_attempt_number;

#         IF v_existing_id IS NOT NULL THEN
#             -- UPDATE existing record
#             UPDATE public.stage2_statement_attempts
#             SET stage2_output_statement = p_stage2_output_statement,
#                 ai_comparison_status = p_ai_comparison_status,
#                 updated_dt = NOW()
#             WHERE attempt_id = v_existing_id;

#             v_attempt_id := v_existing_id;
#         ELSE
#             -- INSERT new record
#             INSERT INTO public.stage2_statement_attempts (
#                 statement_id, attempt_number, stage2_output_statement,
#                 ai_comparison_status, created_dt
#             ) VALUES (
#                 p_statement_id, p_attempt_number, p_stage2_output_statement,
#                 p_ai_comparison_status, NOW()
#             ) RETURNING attempt_id INTO v_attempt_id;
#         END IF;

#         RETURN v_attempt_id;

#     EXCEPTION
#         WHEN OTHERS THEN
#             RAISE EXCEPTION 'Error in sp_insert_update_statement_attempt: %', SQLERRM;
#             RETURN NULL;
#     END;
# END;
# $$;

# ================================ Stage 2 Database Functions ================================
def stage2_statement_metadata_insert(db_data, process_type, tgt_object_id, schema_name, tgt_object_name,
                                     migration_name, object_type, original_error, target_statement_number,
                                     source_statement_number, original_source_statement, target_statement,
                                     ai_converted_statement):
    """
    Insert initial statement metadata during Stage 1 CSV generation.
    Returns the statement_id on success, None on failure.
    """
    connection = None
    cursor = None
    try:
        connection = connect_database(db_data)
        cursor = connection.cursor()

        cursor.execute("""
            SELECT public.sp_stage2_statement_metadata_insert(
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
        """, [
            process_type, tgt_object_id, schema_name, tgt_object_name, migration_name,
            object_type, original_error, target_statement_number, source_statement_number,
            original_source_statement, target_statement, ai_converted_statement
        ])

        result = cursor.fetchone()
        statement_id = result[0] if result else None

        connection.commit()
        print(f"✅ Statement metadata inserted - Statement ID: {statement_id}")
        return statement_id

    except Exception as e:
        print(
            f"❌ Database error in stage2_statement_metadata_insert: {str(e)}")
        if connection:
            connection.rollback()
        return None
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


def stage2_statement_features_update(db_data, process_type, migration_name, schema_name, object_type,
                                     tgt_object_name, target_statement_number, source_before_statement_level,
                                     source_after_statement_level, statement_level_features, program_level_features,
                                     responsible_statement_level_features, responsible_program_level_features):
    """
    Update statement features during identify_responsible_features node.
    Returns the statement_id on success, None on failure.
    """
    connection = None
    cursor = None
    try:
        connection = connect_database(db_data)
        cursor = connection.cursor()

        cursor.execute("""
            SELECT public.sp_stage2_statement_features_update(
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
        """, [
            process_type, migration_name, schema_name, object_type, tgt_object_name, target_statement_number,
            source_before_statement_level, source_after_statement_level, statement_level_features,
            program_level_features, responsible_statement_level_features, responsible_program_level_features
        ])

        result = cursor.fetchone()
        statement_id = result[0] if result else None

        connection.commit()
        print(f"✅ Statement features updated - Statement ID: {statement_id}")
        return statement_id

    except Exception as e:
        print(
            f"❌ Database error in stage2_statement_features_update: {str(e)}")
        if connection:
            connection.rollback()
        return None
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


def insert_update_statement_attempt(db_data, statement_id, attempt_number, stage2_output_statement, ai_comparison_status):
    """
    Insert or update statement attempt record with AI comparison result.
    Returns the attempt_id on success, None on failure.
    """
    connection = None
    cursor = None
    try:
        connection = connect_database(db_data)
        cursor = connection.cursor()

        cursor.execute("""
            SELECT public.sp_insert_update_statement_attempt(%s, %s, %s, %s)
        """, [statement_id, attempt_number, stage2_output_statement, ai_comparison_status])

        result = cursor.fetchone()
        attempt_id = result[0] if result else None

        connection.commit()
        print(
            f"✅ Statement attempt updated - Attempt ID: {attempt_id}, Status: {ai_comparison_status}")
        return attempt_id

    except Exception as e:
        print(f"❌ Database error in insert_update_statement_attempt: {str(e)}")
        if connection:
            connection.rollback()
        return None
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


def insert_stage2_conversion_module(db_data, statement_id, feature_name, original_object_path,
                                    updated_object_path, attempt, is_inregistry, registry_path,
                                    original_module, updated_module, registry_module_code=None):
    """
    Insert or update a stage2_conversion_modules record using stored procedure.

    Args:
        original_object_path: Original QMigrator module path (e.g., Common/Statement/Pre/join.py)
        updated_object_path: Enhanced decomposed module path (e.g., Stage1_Metadata/.../join_attempt_1.py)

    Returns the module_id on success, None on failure.
    """
    connection = None
    cursor = None
    try:
        connection = connect_database(db_data)
        cursor = connection.cursor()

        cursor.execute("""
            SELECT public.sp_insert_stage2_conversion_module(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, [
            statement_id, feature_name, original_object_path, updated_object_path, attempt,
            is_inregistry, registry_path, original_module, updated_module, registry_module_code
        ])

        result = cursor.fetchone()
        module_id = result[0] if result else None

        connection.commit()
        return module_id

    except Exception as e:
        print(f"❌ Database error in insert_stage2_conversion_module: {str(e)}")
        if connection:
            connection.rollback()
        return None
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


def get_stage2_conversion_module_by_id(db_data, module_id):
    """
    Get a single stage2_conversion_modules record by ID with statement info.
    Returns module data dict on success, None on failure.
    """
    connection = None
    cursor = None
    try:
        connection = connect_database(db_data)
        cursor = connection.cursor()

        cursor.execute("""
            SELECT
                m.id, m.statement_id, m.feature_name, m.original_object_path, m.updated_object_path,
                m.is_inregistry, m.registry_path, m.attempt, m.original_module, m.updated_module,
                m.registry_module_code, m.is_reviewed, m.reviewer_comments, m.reviewer_name, m.is_merged,
                s.migration_name, s.schema_name
            FROM public.stage2_conversion_modules m
            INNER JOIN public.stage2_statements s ON s.statement_id = m.statement_id
            WHERE m.id = %s
        """, [module_id])

        result = cursor.fetchone()
        if not result:
            return None

        # Convert to dictionary
        module_data = {
            'id': result[0],
            'statement_id': result[1],
            'feature_name': result[2],
            'original_object_path': result[3],
            'updated_object_path': result[4],
            'is_inregistry': result[5],
            'registry_path': result[6],
            'attempt': result[7],
            'original_module': result[8],
            'updated_module': result[9],
            'registry_module_code': result[10],
            'is_reviewed': result[11],
            'reviewer_comments': result[12],
            'reviewer_name': result[13],
            'is_merged': result[14],
            'migration_name': result[15],
            'schema_name': result[16]
        }

        return module_data

    except Exception as e:
        print(f"❌ Database error in get_stage2_conversion_module_by_id: {str(e)}")
        return None
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


def update_stage2_conversion_module_status(db_data, module_id, is_reviewed=None, reviewer_comments=None,
                                           reviewer_name=None, is_merged=None, updated_module=None, registry_module_code=None,
                                           is_inregistry=None, registry_path=None):
    """
    Update review/merge status for a stage2_conversion_modules record using stored procedure.
    Returns the module_id on success, None on failure.
    """
    connection = None
    cursor = None
    try:
        connection = connect_database(db_data)
        cursor = connection.cursor()

        cursor.execute("""
            SELECT public.sp_update_stage2_conversion_module_status(%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, [
            module_id, is_reviewed, reviewer_comments, reviewer_name, is_merged, updated_module, registry_module_code,
            is_inregistry, registry_path
        ])

        result = cursor.fetchone()
        updated_id = result[0] if result else None

        connection.commit()
        return updated_id

    except Exception as e:
        print(
            f"❌ Database error in update_stage2_conversion_module_status: {str(e)}")
        if connection:
            connection.rollback()
        return None
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


def qbook_request_insert(db_data, process_type, migration_name, schema_name, object_type, object_name, status='Started'):
    """
    Insert or update a QBook request record using stored procedure.
    Returns the request_id on success, None on failure.
    """
    connection = None
    cursor = None
    try:
        connection = connect_database(db_data)
        cursor = connection.cursor()

        cursor.execute("""
            SELECT public.sp_insert_request(%s, %s, %s, %s, %s, %s)
        """, [
            process_type, migration_name, schema_name, object_type, object_name, status
        ])

        result = cursor.fetchone()
        request_id = result[0] if result else None

        connection.commit()
        print(
            f"✅ QBook request insert executed successfully - Request ID: {request_id}")
        return request_id

    except Exception as e:
        print(f"❌ Database error in qbook_request_insert: {str(e)}")
        return None
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


def qbook_request_update(db_data, request_id, status, error=None):
    """
    Update a QBook request record status using request_id directly.
    Returns the request_id on success, None on failure.
    """
    connection = None
    cursor = None
    try:
        connection = connect_database(db_data)
        cursor = connection.cursor()

        cursor.execute("""
            SELECT public.sp_update_request_by_id(%s, %s, %s)
        """, [
            request_id, status, error
        ])

        result = cursor.fetchone()
        updated_id = result[0] if result else None

        connection.commit()
        print(
            f"✅ QBook request update executed successfully - Request ID: {updated_id}, Status: {status}")
        return updated_id

    except Exception as e:
        print(f"❌ Database error in qbook_request_update: {str(e)}")
        return None
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


def get_recent_requests(db_data, limit=50):
    """
    Get recent request records from the requests table.
    Returns the last N records ordered by created_dt descending.

    Args:
        db_data: Database configuration
        limit: Number of records to retrieve (default: 50)

    Returns:
        List of request records on success, None on failure.
    """
    connection = None
    cursor = None
    try:
        connection = connect_database(db_data)
        cursor = connection.cursor()

        query = """
            SELECT
                request_id,
                process_type,
                migration_name,
                schema_name,
                object_type,
                object_name,
                status,
                error,
                created_dt,
                updated_dt
            FROM public.requests
            ORDER BY created_dt DESC
            LIMIT %s
        """

        cursor.execute(query, (limit,))
        results = cursor.fetchall()

        # Convert results to list of dictionaries
        requests_list = []
        for row in results:
            request_record = {
                "request_id": row[0],
                "process_type": row[1],
                "migration_name": row[2],
                "schema_name": row[3],
                "object_type": row[4],
                "object_name": row[5],
                "status": row[6],
                "error": row[7],
                "created_dt": row[8].isoformat() if row[8] else None,
                "updated_dt": row[9].isoformat() if row[9] else None
            }
            requests_list.append(request_record)

        print(f"✅ Retrieved {len(requests_list)} recent request records")
        return requests_list

    except Exception as e:
        print(f"❌ Database error in get_recent_requests: {str(e)}")
        return None
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


def get_encryption_key_by_migration(db_data, migration_name):
    """
    Get the current encryption key for a specific migration name.

    Args:
        db_data: Database configuration
        migration_name: Name of the migration to get the key for

    Returns:
        String encryption key on success, None on failure.
    """
    connection = None
    cursor = None
    try:
        connection = connect_database(db_data)
        cursor = connection.cursor()

        query = """
            SELECT "Current_Encryption_Key"
            FROM public."CookBook_keys"
            WHERE TRIM(LOWER("Migration_Name")) = TRIM(LOWER(%s))
            ORDER BY "Created_at" DESC
            LIMIT 1
        """

        cursor.execute(query, (migration_name,))
        result = cursor.fetchone()

        if result and result[0]:
            encryption_key = result[0]

            # Handle bytes object conversion if needed
            if isinstance(encryption_key, bytes):
                encryption_key = encryption_key.decode('utf-8')

            # Remove b'...' wrapper if present in string
            if encryption_key.startswith("b'") and encryption_key.endswith("'"):
                encryption_key = encryption_key[2:-1]
            elif encryption_key.startswith('b"') and encryption_key.endswith('"'):
                encryption_key = encryption_key[2:-1]

            print(
                f"✅ Retrieved encryption key for migration: {migration_name}")
            return encryption_key
        else:
            print(f"❌ No encryption key found for migration: {migration_name}")
            return None

    except Exception as e:
        print(f"❌ Database error in get_encryption_key_by_migration: {str(e)}")
        return None
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


def get_encryption_key_for_migration(migration_name):
    """
    Get encryption key for a migration from database.

    Args:
        migration_name: Name of the migration

    Returns:
        String encryption key on success, None on failure.
    """
    try:
        db_config = get_qbook_db_config()
        encryption_key = get_encryption_key_by_migration(
            db_config, migration_name)

        if encryption_key:
            return encryption_key
        else:
            print(f"❌ No encryption key found for migration: {migration_name}")
            return None

    except Exception as e:
        print(f"❌ Error getting encryption key for {migration_name}: {str(e)}")
        return None

# ================================ Stage 2 Metadata Functions ================================


def get_schema_names_by_process_type(db_data, process_type, migration_name):
    """
    Get distinct schema names for a given process type and migration name.
    Returns list of schema names on success, empty list on failure.
    """
    connection = None
    cursor = None
    try:
        connection = connect_database(db_data)
        cursor = connection.cursor()

        query = """
            SELECT DISTINCT schema_name
            FROM public.stage2_statements
            WHERE TRIM(LOWER(process_type)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(migration_name)) = TRIM(LOWER(%s))
            ORDER BY schema_name
        """

        cursor.execute(query, (process_type, migration_name))
        results = cursor.fetchall()

        # Extract schema names from tuples
        schema_names = [row[0] for row in results if row[0] is not None]

        return schema_names

    except Exception as e:
        print(
            f"❌ Database error in get_schema_names_by_process_type: {str(e)}")
        return []
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


def get_object_types_by_schema(db_data, process_type, migration_name, schema_name):
    """
    Get distinct object types for a given process type, migration name, and schema name.
    Returns list of object types on success, empty list on failure.
    """
    connection = None
    cursor = None
    try:
        connection = connect_database(db_data)
        cursor = connection.cursor()

        query = """
            SELECT DISTINCT object_type
            FROM public.stage2_statements
            WHERE TRIM(LOWER(process_type)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(migration_name)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(schema_name)) = TRIM(LOWER(%s))
            ORDER BY object_type
        """

        cursor.execute(query, (process_type, migration_name, schema_name))
        results = cursor.fetchall()

        # Extract object types from tuples
        object_types = [row[0] for row in results if row[0] is not None]

        return object_types

    except Exception as e:
        print(f"❌ Database error in get_object_types_by_schema: {str(e)}")
        return []
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


def get_object_names_by_type(db_data, process_type, migration_name, schema_name, object_type):
    """
    Get distinct object names for a given process type, migration name, schema name, and object type.
    Returns list of object names on success, empty list on failure.
    """
    connection = None
    cursor = None
    try:
        connection = connect_database(db_data)
        cursor = connection.cursor()

        query = """
            SELECT DISTINCT tgt_object_name
            FROM public.stage2_statements
            WHERE TRIM(LOWER(process_type)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(migration_name)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(schema_name)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(object_type)) = TRIM(LOWER(%s))
            ORDER BY tgt_object_name
        """

        cursor.execute(query, (process_type, migration_name,
                       schema_name, object_type))
        results = cursor.fetchall()

        # Extract object names from tuples
        object_names = [row[0] for row in results if row[0] is not None]

        return object_names

    except Exception as e:
        print(f"❌ Database error in get_object_names_by_type: {str(e)}")
        return []
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


def get_target_statement_numbers(db_data, process_type, migration_name, schema_name, object_type, object_name):
    """
    Get distinct target statement numbers for dropdown.
    Returns list of target statement numbers on success, empty list on failure.
    """
    connection = None
    cursor = None
    try:
        connection = connect_database(db_data)
        cursor = connection.cursor()

        query = """
            SELECT target_statement_number
            FROM public.stage2_statements
            WHERE TRIM(LOWER(process_type)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(migration_name)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(schema_name)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(object_type)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(tgt_object_name)) = TRIM(LOWER(%s))
            ORDER BY created_dt
        """

        cursor.execute(query, (process_type, migration_name,
                       schema_name, object_type, object_name))
        results = cursor.fetchall()

        # Extract target statement numbers from tuples
        target_statements = [row[0] for row in results if row[0] is not None]

        return target_statements

    except Exception as e:
        print(f"❌ Database error in get_target_statement_numbers: {str(e)}")
        return []
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()


def get_statement_with_modules(db_data, target_statement_number, process_type, migration_name, schema_name, object_type, object_name):
    """
    Get complete statement data with all corresponding module attempts AND attempt results.
    Uses optimized three-query approach to avoid data duplication.
    Returns dict with statement data and nested attempts structure with attempt results on success, None on failure.

    Return structure:
    {
        "statement_id": 123,
        "process_type": "qbook",
        ... (other statement fields),
        "attempts": {
            "5": {
                "modules": {
                    "nvl": {
                        "id": 101,
                        "feature_name": "nvl",
                        "original_object_path": "Common/Statement/Pre/nvl.py",
                        "updated_object_path": "Stage1_Metadata/.../nvl_attempt_5.py",
                        "original_module": "...",
                        "updated_module": "...",
                        "is_reviewed": false,
                        "is_merged": false,
                        ...
                    },
                    "order_by": {
                        "id": 102,
                        "feature_name": "order_by",
                        "original_object_path": "Common/Statement/Pre/order_by.py",
                        "updated_object_path": "Stage1_Metadata/.../order_by_attempt_5.py",
                        "original_module": "...",
                        "updated_module": "...",
                        ...
                    }
                },
                "attempt_result": {
                    "attempt_id": 15,
                    "stage2_output_statement": "SELECT * FROM ...",
                    "ai_comparison_status": "Success"
                }
            },
            "4": {
                "modules": {
                    "nvl": {
                        "id": 103,
                        "feature_name": "nvl",
                        "original_object_path": "Common/Statement/Pre/nvl.py",
                        "updated_object_path": "Stage1_Metadata/.../nvl_attempt_4.py",
                        "original_module": "...",
                        ...
                    }
                },
                "attempt_result": {
                    "attempt_id": 14,
                    "stage2_output_statement": "SELECT * FROM ...",
                    "ai_comparison_status": "Failed"
                }
            }
        }
    }
    """
    connection = None
    cursor = None
    try:
        connection = connect_database(db_data)
        cursor = connection.cursor()

        # First query: Get statement data only (no duplicates)
        statement_query = """
            SELECT
                statement_id,
                process_type,
                tgt_object_id,
                schema_name,
                tgt_object_name,
                migration_name,
                object_type,
                original_error,
                target_statement_number,
                source_statement_number,
                original_source_statement,
                target_statement,
                ai_converted_statement
            FROM public.stage2_statements
            WHERE target_statement_number = %s
              AND TRIM(LOWER(process_type)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(migration_name)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(schema_name)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(object_type)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(tgt_object_name)) = TRIM(LOWER(%s))
        """

        cursor.execute(statement_query, (target_statement_number, process_type,
                       migration_name, schema_name, object_type, object_name))
        statement_result = cursor.fetchone()

        if not statement_result:
            return None

        # Second query: Get modules data only
        modules_query = """
            SELECT
                m.id,
                m.feature_name,
                m.original_object_path,
                m.updated_object_path,
                m.attempt,
                m.is_inregistry,
                m.registry_path,
                m.original_module,
                m.updated_module,
                m.registry_module_code,
                m.is_reviewed,
                m.reviewer_comments,
                m.reviewer_name,
                m.is_merged
            FROM public.stage2_conversion_modules m
            INNER JOIN public.stage2_statements s ON s.statement_id = m.statement_id
            WHERE s.target_statement_number = %s
              AND TRIM(LOWER(s.process_type)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(s.migration_name)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(s.schema_name)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(s.object_type)) = TRIM(LOWER(%s))
              AND TRIM(LOWER(s.tgt_object_name)) = TRIM(LOWER(%s))
            ORDER BY m.attempt DESC, m.feature_name ASC
        """

        cursor.execute(modules_query, (target_statement_number, process_type,
                       migration_name, schema_name, object_type, object_name))
        modules_results = cursor.fetchall()

        statement_id = statement_result[0]

        # Third query: Get attempt results
        attempts_query = """
            SELECT
                attempt_id,
                attempt_number,
                stage2_output_statement,
                ai_comparison_status
            FROM public.stage2_statement_attempts
            WHERE statement_id = %s
            ORDER BY attempt_number DESC
        """

        cursor.execute(attempts_query, (statement_id,))
        attempts_results = cursor.fetchall()

        # Create combined data from statement result
        combined_data = {
            # Statement fields (flattened at root level)
            "statement_id": statement_result[0],
            "process_type": statement_result[1],
            "tgt_object_id": statement_result[2],
            "schema_name": statement_result[3],
            "tgt_object_name": statement_result[4],
            "migration_name": statement_result[5],
            "object_type": statement_result[6],
            "original_error": statement_result[7],
            "target_statement_number": statement_result[8],
            "source_statement_number": statement_result[9],
            "original_source_statement": statement_result[10],
            "target_statement": statement_result[11],
            "ai_converted_statement": statement_result[12],

            # Attempts structure will be populated from modules
            "attempts": {}
        }

        # Process modules_results and create attempt structure with modules nested
        attempts = {}
        for row in modules_results:
            attempt_num = str(row[4])  # attempt number
            feature_name = row[1]      # feature_name

            # Initialize attempt structure if not exists
            if attempt_num not in attempts:
                attempts[attempt_num] = {
                    "modules": {},
                    "attempt_result": None  # Will be populated from attempts_results
                }

            # Add module to the modules section
            attempts[attempt_num]["modules"][feature_name] = {
                "id": row[0],                    # id
                "feature_name": row[1],          # feature_name
                "original_object_path": row[2],  # original_object_path
                "updated_object_path": row[3],   # updated_object_path
                "attempt": row[4],               # attempt
                "is_inregistry": row[5],         # is_inregistry
                "registry_path": row[6],         # registry_path
                "original_module": row[7],       # original_module
                "updated_module": row[8],        # updated_module
                "registry_module_code": row[9],  # registry_module_code
                "is_reviewed": row[10],          # is_reviewed
                "reviewer_comments": row[11],    # reviewer_comments
                "reviewer_name": row[12],        # reviewer_name
                "is_merged": row[13]             # is_merged
            }

        # Process attempt results and add to each attempt
        for row in attempts_results:
            attempt_num = str(row[1])  # attempt_number
            attempt_result = {
                "attempt_id": row[0],
                "attempt_number": row[1],
                "stage2_output_statement": row[2],
                "ai_comparison_status": row[3]
            }

            # Add attempt_result to existing attempt or create new attempt structure
            if attempt_num in attempts:
                attempts[attempt_num]["attempt_result"] = attempt_result
            else:
                # Case where we have attempt result but no modules (shouldn't happen normally)
                attempts[attempt_num] = {
                    "modules": {},
                    "attempt_result": attempt_result
                }

        # Add attempts to combined_data
        combined_data["attempts"] = attempts

        return combined_data

    except Exception as e:
        print(f"❌ Database error in get_statement_with_modules: {str(e)}")
        return None
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
