"""
Stage 2 Workflow State Management for QMigrator Module Updates.

This module defines the state management for Stage 2 processing, which handles
AI corrections from Stage 1 to update QMigrator Python modules through a
streamlined 9-node pipeline workflow with comprehensive retry mechanisms.
"""

from typing import List, Optional, Dict, Any, Literal
from pydantic import BaseModel, Field


# ==================== STRUCTURED OUTPUT MODELS FOR AI RESPONSES ====================

class ResponsibleFeature(BaseModel):
    """
    Pydantic model for a single responsible feature identified by AI.

    Represents a Python module that is responsible for conversion issues,
    including detailed analysis of why it's responsible and its impact.
    """
    feature_name: str = Field(description="Name of the responsible feature/module")
    module_path: str = Field(description="Relative path to the Python module")
    responsibility_reason: str = Field(description="Detailed explanation of why this module is responsible")
    error_impact: str = Field(description="Impact level: High, Medium, Low")
    keywords_matched: List[str] = Field(description="Keywords that triggered this module identification")
    confidence_score: float = Field(description="AI confidence score between 0.0 and 1.0")


class ResponsibleFeaturesAnalysisOutput(BaseModel):
    """
    Simplified Pydantic model for AI-generated responsible features analysis.

    Focuses on responsible features and detailed analysis summary for Excel logging.
    """
    responsible_features: List[ResponsibleFeature] = Field(description="List of identified responsible features with detailed analysis")
    analysis_summary: str = Field(description="Detailed explanation of the analysis process, findings, and reasoning")


class ModuleEnhancementOutput(BaseModel):
    """
    Pydantic model for AI module enhancement results.

    Preserve-first additive approach - original module + supplementary logic.
    """
    enhanced_code: str = Field(description="Enhanced Python module with minimal changes - only add logic where content gap exists, do not repeat existing working code")
    analysis: str = Field(description="Detailed explanation of: 1) What the existing module already handles correctly, 2) What specific content gap was identified (ignoring formatting), 3) What minimal logic was added to bridge the gap, 4) How the enhancement handles similar content variations, 5) Confirmation that output matches AI expected CONTENT (ignoring formatting differences)")
    code_changed: bool = Field(description="True if the module logic was actually modified/enhanced, False if the same logic was reproduced without changes")









class TransformationChange(BaseModel):
    """Model for specific transformation changes needed."""
    source_pattern: str = Field(description="Current pattern that needs to change")
    target_pattern: str = Field(description="What it should become")
    transformation_method: str = Field(description="How to implement the change")
    responsible_module: str = Field(description="Which specific module should handle this transformation")
    variations_to_handle: List[str] = Field(description="List of variations to handle")

class ParameterMapping(BaseModel):
    """Model for parameter mapping guidance."""
    extraction: str = Field(description="How to extract variable components")
    reconstruction: str = Field(description="How to reconstruct in target format")

class ModuleSpecificGuidance(BaseModel):
    """Model for module-specific transformation guidance."""
    patterns_to_handle: List[str] = Field(description="List of patterns this module should handle")
    implementation_focus: str = Field(description="What this specific module should focus on")

class TransformationGuidance(BaseModel):
    """Model for transformation guidance when statements don't match."""
    required: bool = Field(description="Whether transformation is needed")
    specific_changes: List[TransformationChange] = Field(description="List of specific changes needed")
    implementation_steps: List[str] = Field(description="Step-by-step implementation guidance")
    parameter_mapping: ParameterMapping = Field(description="Parameter mapping guidance")
    module_specific_guidance: Dict[str, ModuleSpecificGuidance] = Field(description="Module-specific guidance mapping", default={})

class StatementComparisonOutput(BaseModel):
    """
    Pydantic model for AI statement comparison results.

    Compares AI corrected statement vs applied modules statement for functional equivalence.
    """
    statements_match: bool = Field(description="Whether the statements are functionally equivalent")
    explanation: str = Field(description="Detailed explanation of the comparison analysis and feedback for improvements")
    transformation_guidance: TransformationGuidance = Field(description="Detailed guidance for transforming statements when they don't match")


class Stage2WorkflowState(BaseModel):
    """
    Comprehensive workflow state for Stage 2 QMigrator module update processing.

    Implements the streamlined 9-node pipeline workflow with comprehensive retry
    mechanisms and feedback integration for AI-driven module enhancement.
    """

    # Core input parameters
    migration_name: str = Field(description="Migration name (e.g., Oracle_Postgres14)")
    process_type: Literal["qmigrator", "qbook"] = Field(description="Process type: qmigrator (object-level) or qbook (statement-level)")
    schema_name: str = Field(description="Schema name for the object")
    object_name: str = Field(description="Name of the object being processed")
    cloud_category: str = Field(description="Cloud category: 'local' or 'cloud'")

    # Process type specific fields
    objecttype: Optional[str] = Field(default=None, description="Type of the object (required for qmigrator)")
    source_statement: Optional[str] = Field(default=None, description="Individual source statement (required for qbook)")
    converted_statement: Optional[str] = Field(default=None, description="Individual converted statement (required for qbook)")

    # Optional fields
    target_object_id: Optional[int] = Field(default=None, description="Target object ID from Stage 1")

    # Source and target code
    source_code: Optional[str] = Field(default=None, description="Complete source code")
    target_code: Optional[str] = Field(default=None, description="Complete target code")

    # Processing results
    approved_statements: Optional[List[Dict[str, Any]]] = Field(default=None, description="List of approved statements from Stage 1")
    object_level_features: Optional[List[Dict[str, Any]]] = Field(default=None, description="Object-level feature analysis results")
    available_features_with_statements: Optional[List[Dict[str, Any]]] = Field(default=None, description="Combined approved statements with features")

    # Current processing state
    current_statement_index: int = Field(default=0, description="Index of current statement being processed")
    current_statement: Optional[Dict[str, Any]] = Field(default=None, description="Current statement being processed")

    # Feature identification
    responsible_features: Optional[List[tuple]] = Field(default=None, description="Responsible statement-level features as (name, path) tuples")
    responsible_procedures: Optional[List[str]] = Field(default=None, description="Responsible program-level features")

    # Module updates
    updated_modules: Optional[List[Dict[str, Any]]] = Field(default=None, description="Updated Python modules")

    updated_ai_converted_statement: Optional[str] = Field(default=None, description="Statement after applying updated modules")

    # Attempt tracking
    current_attempt: int = Field(default=1, description="Current attempt number (max 5)")
    max_attempts: int = Field(default=5, description="Maximum attempts per statement")

    # Validation and execution specific attempt tracking
    validation_attempt: int = Field(default=1, description="Current validation retry attempt")
    execution_attempt: int = Field(default=1, description="Current execution retry attempt")

    # Database tracking
    current_statement_id: Optional[int] = Field(default=None, description="Database ID of current statement")

    # Comments handling
    comments_dict: Optional[Dict[str, str]] = Field(default=None, description="Dictionary mapping comment markers to original comments")

    # Workflow control flags

    ai_statements_match: bool = Field(default=False, description="AI statements comparison passed")
    workflow_action: Optional[str] = Field(default=None, description="Workflow routing action: next_statement, retry_current, or complete")

    # AI comparison feedback for new pipeline
    ai_comparison_feedback: Optional[Dict[str, Any]] = Field(default=None, description="Feedback from AI statement comparison for enhancement retry")

    # In-memory attempt history storage
    attempt_history: List[Dict[str, Any]] = Field(default_factory=list, description="In-memory attempt history for current statement")

    # Registry Integration Fields
    registry_status: Optional[str] = Field(default=None, description="Registry status: all_in_registry, some_in_registry, none_in_registry")
    registry_modules: Optional[List[tuple]] = Field(default=None, description="Modules found in registry as (feature_name, module_path, responsibility_reason, keywords) tuples")
    missing_modules: Optional[List[tuple]] = Field(default=None, description="Modules not in registry as (feature_name, module_path, responsibility_reason, keywords) tuples")
    enhanced_modules_loaded: bool = Field(default=False, description="Whether enhanced modules were loaded from registry")
    module_source: Optional[str] = Field(default="original", description="Source of modules: enhanced, original, mixed")
    execution_source: Optional[str] = Field(default="enhancement", description="Execution source: registry, mixed, enhancement")
    registry_updated: bool = Field(default=False, description="Whether registry was updated with new enhanced modules")
    saved_modules: Optional[List[str]] = Field(default=None, description="List of module names saved to registry")
    skipped_modules: Optional[List[str]] = Field(default=None, description="List of module names skipped (unchanged) during registry save")
    total_modules_processed: int = Field(default=0, description="Total number of modules processed for registry save")

    # Final results
    completed_statements: List[Dict[str, Any]] = Field(default_factory=list, description="Successfully processed statements")
    failed_statements: List[Dict[str, Any]] = Field(default_factory=list, description="Statements that failed after max attempts")
    workflow_completed: bool = Field(default=False, description="Entire workflow completed")

    # Excel tracking
    stage2_excel_path: Optional[str] = Field(default=None, description="Temp path to Stage 2 Excel file")
    final_qbook_excel_path: Optional[str] = Field(default=None, description="Final QBook path for Stage 2 Excel file")
    metadata_dir: Optional[str] = Field(default=None, description="Stage 1 metadata directory path")
    temp_dir: Optional[str] = Field(default=None, description="Temp directory for Stage 2 processing")

    # New pipeline state fields
    module_categories: Optional[Dict[str, List[tuple]]] = Field(default=None, description="Categorized modules: pre_execution, responsible, post_execution")
    pre_processed_output: Optional[str] = Field(default=None, description="Output after pre-feature execution")
    driver_module_code: Optional[str] = Field(default=None, description="Combined driver module code")
    driver_module_path: Optional[str] = Field(default=None, description="Path to saved driver module")
    enhanced_driver_code: Optional[str] = Field(default=None, description="AI-enhanced driver module code")
    enhanced_driver_path: Optional[str] = Field(default=None, description="Path to saved enhanced driver module")
    decomposed_modules: Optional[List[Dict[str, Any]]] = Field(default=None, description="Decomposed individual modules with change detection")
    validation_results: Optional[List[Dict[str, Any]]] = Field(default=None, description="Module validation results")
    validation_passed: bool = Field(default=False, description="Overall validation status")
    execution_success: bool = Field(default=False, description="Pipeline execution success status")
    final_output: Optional[str] = Field(default=None, description="Final pipeline output")
    statements_match: bool = Field(default=False, description="AI comparison result")
    iteration_action: Optional[str] = Field(default=None, description="Iteration control action: proceed, retry, fail")

    # Enhanced feedback fields
    validation_feedback: Optional[Dict[str, Any]] = Field(default=None, description="Validation feedback for enhancement retry")
    execution_feedback: Optional[Dict[str, Any]] = Field(default=None, description="Execution feedback for enhancement retry")
    ai_comparison_feedback: Optional[Dict[str, Any]] = Field(default=None, description="AI comparison feedback for enhancement retry")
