import uuid
import os
import zipfile
import tempfile
import glob
from fastapi import APIRouter, HTTPException, Form, BackgroundTasks
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import Optional

# Import configuration and LLM components
from llm_config.config_manager import ConfigManager
from common.common import create_llm
from config import Config

# Import Stage 2 workflow components
from Conversion_Agent_Stage2.workflow.graph_builder import Stage2GraphBuilder

# Import database functions
from common.database import (
    update_stage2_conversion_module_status,
    get_stage2_conversion_module_by_id,
    get_qbook_db_config,
    get_schema_names_by_process_type,
    get_object_types_by_schema,
    get_object_names_by_type,
    get_target_statement_numbers,
    get_statement_with_modules,
    qbook_request_insert,
    qbook_request_update,
    get_recent_requests
)

# Import registry utilities
from Conversion_Agent_Stage2.utils.registry_utils import (
    get_registry_path,
    save_enhanced_module_to_registry
)

# Import encryption utilities
from common.database import get_encryption_key_for_migration
from cryptography.fernet import <PERSON><PERSON><PERSON>
from config import Config
import os

router = APIRouter()


def encrypt_and_save_module(module_code: str, file_path: str, migration_name: str) -> bool:
    """
    Encrypt module code and save to QBook file system following existing patterns.

    Args:
        module_code: Module code to encrypt and save
        file_path: Full path where to save the encrypted file
        migration_name: Migration name to get encryption key

    Returns:
        True if saved successfully, False otherwise
    """
    try:
        # Get encryption key for migration
        encryption_key = get_encryption_key_for_migration(migration_name)
        if not encryption_key:
            print(f"❌ No encryption key found for migration: {migration_name}")
            return False

        # Create Fernet cipher
        f = Fernet(encryption_key)

        # Encrypt the module code
        encrypted_data = f.encrypt(module_code.encode('utf-8'))

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # Save encrypted data to file
        with open(file_path, 'wb') as file:
            file.write(encrypted_data)

        print(f"🔐 Encrypted and saved module to: {file_path}")
        return True

    except Exception as e:
        print(f"❌ Error encrypting and saving module: {str(e)}")
        return False


class Stage2ConversionData:
    """Data class for Stage 2 conversion request parameters."""
    def __init__(self, process_type, schema_name, objecttype, object_name, migration_name, cloud_category,
                 source_statement=None, converted_statement=None):
        self.process_type = process_type
        self.schema_name = schema_name
        self.objecttype = objecttype
        self.object_name = object_name
        self.migration_name = migration_name
        self.cloud_category = cloud_category
        # QBook-specific parameters
        self.source_statement = source_statement
        self.converted_statement = converted_statement


def run_stage2_conversion_workflow(request_data: Stage2ConversionData):
    """Execute Stage 2 conversion workflow to update QMigrator modules."""
    request_id = None
    try:
        print(f"🚀 Starting Stage 2 conversion workflow...")

        # Insert request record with 'Started' status
        request_id = qbook_request_insert(
            db_data=get_qbook_db_config(),
            process_type=request_data.process_type,
            migration_name=request_data.migration_name,
            schema_name=request_data.schema_name,
            object_type=request_data.objecttype if request_data.process_type == "qmigrator" else "Statement",
            object_name=request_data.object_name,
            status='Started'
        )
        print(f"📝 QBook request tracking started - Request ID: {request_id}")

        # Initialize LLM configuration
        config_manager = ConfigManager()
        llm_provider = config_manager.get_llm_provider()
        print(f"🔧 Initializing {llm_provider} LLM for Stage 2...")
        llm = create_llm(llm_provider, config_manager)
        print(f"✅ Successfully initialized {llm_provider} for Stage 2 processing")

        # Initialize the Stage 2 workflow graph builder with the LLM
        graph_builder = Stage2GraphBuilder(llm)
        graph_builder.setup_graph()

        # Generate workflow visualization for debugging and documentation
        graph_builder.save_graph_image(graph_builder.graph)

        # Generate unique thread ID for this Stage 2 workflow
        thread_id = str(uuid.uuid4())
        print(f"🧵 Stage 2 workflow thread ID: {thread_id}")

        # Execute the Stage 2 workflow with initial state
        print("🔄 Starting Stage 2 workflow execution...")

        # Prepare workflow state based on process type
        workflow_state = {
            "migration_name": request_data.migration_name,
            "process_type": request_data.process_type,
            "schema_name": request_data.schema_name,
            "object_name": request_data.object_name,
            "cloud_category": request_data.cloud_category,
            "current_statement_index": 0,
            "current_attempt": 1,
            "max_attempts": 2,
            "features_validation_passed": False,
            "responsible_features_valid": False,

            "ai_statements_match": False,
            "workflow_completed": False,
            "attempt_history": []
        }

        # Add process type specific parameters
        if request_data.process_type == "qmigrator":
            workflow_state["objecttype"] = request_data.objecttype
        elif request_data.process_type == "qbook":
            workflow_state["source_statement"] = request_data.source_statement
            workflow_state["converted_statement"] = request_data.converted_statement

        result = graph_builder.invoke_graph(workflow_state, thread_id=thread_id)

        print("✅ Stage 2 workflow completed successfully")
        # print(f"📊 Final result: {result}")

        # Update request status to 'Completed' on success
        if request_id:
            qbook_request_update(
                db_data=get_qbook_db_config(),
                request_id=request_id,
                status='Completed'
            )
            print(f"📝 QBook request marked as Completed - Request ID: {request_id}")

        return result

    except Exception as e:
        print(f"❌ Stage 2 workflow failed: {str(e)}")

        # Update request status to 'Error' on failure
        if request_id:
            qbook_request_update(
                db_data=get_qbook_db_config(),
                request_id=request_id,
                status='Error',
                error=str(e)
            )
            print(f"📝 QBook request marked as Error - Request ID: {request_id}")

        raise
    

@router.post("/conversion-agent-stage2")
def conversion_agent_stage2(
    background_tasks: BackgroundTasks,
    process_type: str = Form(..., description="Process type: 'qmigrator' for object-level or 'qbook' for statement-level"),
    migration_name: str = Form(..., description="Name of the migration"),
    schema_name: str = Form(..., description="Schema name for the database object"),
    object_name: str = Form(..., description="Name of the object being processed"),
    cloud_category: str = Form(..., description="Cloud category: 'local' or 'cloud'"),
    # QMigrator-specific parameters (optional, required only for qmigrator process type)
    objecttype: str = Form(None, description="Type of the object being processed (required for qmigrator process type)"),
    # QBook-specific parameters (optional, required only for qbook process type)
    source_statement: str = Form(None, description="Individual source statement (required for qbook process type)"),
    converted_statement: str = Form(None, description="Individual converted statement (required for qbook process type)")
):
    """
    Stage 2 Conversion Agent - QMigrator Module Updates.

    Processes AI corrections from Stage 1 to update QMigrator Python modules
    through a workflow with retry mechanism.

    Common Parameters:
    - process_type: 'qmigrator' for object-level or 'qbook' for statement-level processing
    - migration_name: Name of the migration (e.g., Oracle_Postgres14)
    - schema_name: Schema name for the database object
    - object_name: Name of the object being processed

    QMigrator Process Type - Additional Required:
    - objecttype: Type of the object (e.g., procedure, function, view)

    QBook Process Type - Additional Required:
    - source_statement: Single source statement for processing
    - converted_statement: Single converted statement for processing

    Note: QBook processes ONE statement at a time. QMigrator reads files during execution.
    """

    try:
        print(f"🚀 Starting Stage 2 conversion workflow...")
        print(f"🔀 Process type: {process_type}")

        # Validate process type specific mandatory parameters
        if process_type == "qmigrator":
            # QMigrator mandatory parameters: schema_name, object_name, objecttype, migration_name
            if not objecttype:
                raise HTTPException(
                    status_code=400,
                    detail={
                        "error": "Missing required parameters for qmigrator process type",
                        "message": "objecttype is required for qmigrator processing",
                        "required_parameters": ["schema_name", "object_name", "objecttype", "migration_name"]
                    }
                )
            print(f"📁 QMigrator processing - Object type: {objecttype}")

        elif process_type == "qbook":
            # QBook mandatory parameters: schema_name, object_name, source_statement, converted_statement, migration_name
            if not source_statement or not converted_statement:
                raise HTTPException(
                    status_code=400,
                    detail={
                        "error": "Missing required parameters for qbook process type",
                        "message": "source_statement and converted_statement are required for qbook processing",
                        "required_parameters": ["schema_name", "object_name", "source_statement", "converted_statement", "migration_name"]
                    }
                )
            print(f"📝 QBook single statement processing - source_statement length: {len(source_statement)}")
            print(f"📝 QBook single statement processing - converted_statement length: {len(converted_statement)}")
            print("📋 QBook mode: Processing ONE statement only")
        else:
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "Invalid process type",
                    "message": f"process_type must be 'qmigrator' or 'qbook', got: {process_type}",
                    "valid_process_types": ["qmigrator", "qbook"]
                }
            )

        # Create Stage 2 request object from form data
        request_data = Stage2ConversionData(
            process_type=process_type,
            schema_name=schema_name,
            objecttype=objecttype,
            object_name=object_name,
            migration_name=migration_name,
            cloud_category=cloud_category,
            source_statement=source_statement,
            converted_statement=converted_statement
        )

        # Execute the Stage 2 conversion workflow as background task
        background_tasks.add_task(run_stage2_conversion_workflow, request_data)

        # Return immediate response - workflow running in background
        response = {
            "message": "Stage 2 Conversion Agent Process Started Successfully",
            "status": "started",
            "stage": "Stage 2 - QMigrator Module Updates",
            "process_type": process_type,
            "schema_name": schema_name,
            "migration_name": migration_name,
            "object_name": object_name,
            "objecttype": objecttype,
            "cloud_category": cloud_category,
            "note": "Workflow is running in background. Check logs for progress updates."
        }

        # Add qbook-specific parameters to response if applicable
        if process_type == "qbook":
            response["source_statement_length"] = len(source_statement) if source_statement else 0
            response["converted_statement_length"] = len(converted_statement) if converted_statement else 0

        return response

    except Exception as e:
        print(f"❌ Failed to start Stage 2 conversion workflow: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to start Stage 2 conversion workflow",
                "message": str(e),
                "stage": "Stage 2 - QMigrator Module Updates"
            }
        )


# Pydantic model for module review update request
class ModuleReviewUpdateRequest(BaseModel):
    module_id: int
    is_reviewed: Optional[bool] = None
    reviewer_comments: Optional[str] = None
    reviewer_name: Optional[str] = None
    is_merged: Optional[bool] = None
    updated_module: Optional[str] = None  # For code changes during review
    registry_module_code: Optional[str] = None  # Enhanced module code from registry


# Pydantic model for move to registry request
class MoveToRegistryRequest(BaseModel):
    module_id: int
    source_selection: str  # "updated_module" or "registry_module_code"
    cloud_category: str  # "local" or "cloud" - following existing pattern
    reviewer_name: str
    comments: Optional[str] = "Moved to registry"


# Pydantic model for merge to QBook request
class MergeToQBookRequest(BaseModel):
    module_id: int
    cloud_category: str  # "local" or "cloud" - following existing pattern
    reviewer_name: str
    comments: Optional[str] = "Merged to QBook"


@router.post("/update-module-review-status")
async def update_module_review_status(request: ModuleReviewUpdateRequest):
    """
    Update the review status of a Stage 2 conversion module.
    This endpoint is used for manual reviewer updates.
    """
    try:
        print(f"🔍 Updating review status for module ID: {request.module_id}")

        # Call the database function to update module status
        updated_module_id = update_stage2_conversion_module_status(
            db_data=get_qbook_db_config(),
            module_id=request.module_id,
            is_reviewed=request.is_reviewed,
            reviewer_comments=request.reviewer_comments,
            reviewer_name=request.reviewer_name,
            is_merged=request.is_merged,
            updated_module=request.updated_module,
            registry_module_code=request.registry_module_code,
            is_inregistry=None,  # Not updated in review API
            registry_path=None   # Not updated in review API
        )

        if updated_module_id:
            print(f"✅ Successfully updated module review status for ID: {updated_module_id}")

            # Prepare response with updated fields
            updated_fields = {}
            if request.is_reviewed is not None:
                updated_fields["is_reviewed"] = request.is_reviewed
            if request.reviewer_comments is not None:
                updated_fields["reviewer_comments"] = request.reviewer_comments
            if request.reviewer_name is not None:
                updated_fields["reviewer_name"] = request.reviewer_name
            if request.is_merged is not None:
                updated_fields["is_merged"] = request.is_merged
            if request.updated_module is not None:
                updated_fields["updated_module"] = "Code updated"
            if request.registry_module_code is not None:
                updated_fields["registry_module_code"] = "Registry module code updated"

            return {
                "message": "Module review status updated successfully",
                "status": "success",
                "module_id": updated_module_id,
                "updated_fields": updated_fields
            }
        else:
            print(f"❌ Failed to update module review status for ID: {request.module_id}")
            raise HTTPException(
                status_code=404,
                detail={
                    "error": "Module not found or update failed",
                    "message": f"No module found with ID: {request.module_id}",
                    "module_id": request.module_id
                }
            )

    except Exception as e:
        print(f"❌ Error updating module review status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to update module review status",
                "message": str(e),
                "module_id": request.module_id
            }
        )


@router.post("/move-to-registry")
async def move_module_to_registry(request: MoveToRegistryRequest):
    """
    Move selected module code to registry for future reuse.

    User selects which code to move via dropdown:
    - "updated_module": Latest AI enhancement
    - "registry_module_code": Current registry version

    Requirements:
    - Module must be reviewed (is_reviewed = true)
    - Selected source must exist and not be empty
    - cloud_category must be provided (local/cloud)
    - Updates database and file system registry
    """
    try:
        print(f"🗂️ Moving module to registry - Module ID: {request.module_id}, Source: {request.source_selection}, Cloud: {request.cloud_category}")

        # 1. Validate source selection
        if request.source_selection not in ["updated_module", "registry_module_code"]:
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "Invalid source selection",
                    "message": "Source must be 'updated_module' or 'registry_module_code'",
                    "provided_source": request.source_selection
                }
            )

        # 2. Validate cloud_category
        if request.cloud_category.lower() not in ["local", "cloud"]:
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "Invalid cloud category",
                    "message": "Cloud category must be 'local' or 'cloud'",
                    "provided_cloud_category": request.cloud_category
                }
            )

        # 3. Get current module data
        module_data = get_stage2_conversion_module_by_id(
            db_data=get_qbook_db_config(),
            module_id=request.module_id
        )

        if not module_data:
            print(f"❌ Module not found with ID: {request.module_id}")
            raise HTTPException(
                status_code=404,
                detail={
                    "error": "Module not found",
                    "message": f"No module found with ID: {request.module_id}",
                    "module_id": request.module_id
                }
            )

        # 4. Validate module is reviewed
        if not module_data.get('is_reviewed', False):
            print(f"❌ Module not reviewed - ID: {request.module_id}")
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "Module not reviewed",
                    "message": "Module must be reviewed before moving to registry",
                    "module_id": request.module_id,
                    "is_reviewed": module_data.get('is_reviewed', False)
                }
            )

        # 5. Get selected source code
        if request.source_selection == "updated_module":
            source_code = module_data.get('updated_module', '')
            source_description = "Latest AI enhancement"
        else:  # registry_module_code
            source_code = module_data.get('registry_module_code', '')
            source_description = "Current registry version"

        # 6. Validate source code exists and is not empty
        if not source_code or source_code.strip() == "":
            print(f"❌ No {request.source_selection} code available - ID: {request.module_id}")
            raise HTTPException(
                status_code=400,
                detail={
                    "error": f"No {request.source_selection} code",
                    "message": f"No {source_description.lower()} code available to move to registry",
                    "module_id": request.module_id,
                    "source_selection": request.source_selection
                }
            )

        # 7. Determine registry path
        original_object_path = module_data.get('original_object_path')
        migration_name = module_data.get('migration_name')
        existing_registry_path = module_data.get('registry_path')

        # Use cloud_category from request (following existing pattern)
        cloud_category = request.cloud_category

        if existing_registry_path and existing_registry_path.strip():
            # Convert relative registry path from database to absolute path for file operations
            if cloud_category.lower() == 'local':
                base_path = Config.Qbook_Local_Path
            else:
                base_path = Config.Qbook_Path
            registry_path = os.path.join(base_path, existing_registry_path).replace("\\", "/")
            action = "updated"
            print(f"📁 Using existing registry path (converted to absolute): {registry_path}")
        else:
            # Generate new registry path (absolute for file operations)
            registry_path = get_registry_path(
                original_object_path,
                migration_name,
                cloud_category
            )
            action = "created"
            print(f"🆕 Generated new registry path: {registry_path}")

        print(f"📋 Moving {source_description.lower()} to registry...")

        # 8. Update database - set registry_module_code to selected source
        # Convert absolute registry path to relative for database storage
        if cloud_category.lower() == 'local':
            base_path = Config.Qbook_Local_Path
        else:
            base_path = Config.Qbook_Path
        registry_relative_path = os.path.relpath(registry_path, base_path).replace("\\", "/")

        updated_module_id = update_stage2_conversion_module_status(
            db_data=get_qbook_db_config(),
            module_id=request.module_id,
            registry_module_code=source_code,  # Copy selected source to registry_module_code
            is_inregistry=True,  # Mark as in registry
            registry_path=registry_relative_path,  # Set registry path (RELATIVE)
            reviewer_name=request.reviewer_name,
            reviewer_comments=f"Moved {source_description.lower()} to registry: {request.comments}"
        )

        if not updated_module_id:
            print(f"❌ Failed to update database - ID: {request.module_id}")
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "Database update failed",
                    "message": "Failed to update module registry information in database",
                    "module_id": request.module_id
                }
            )

        # 9. Save to file system registry
        print(f"📁 Saving {source_description.lower()} to registry file system...")
        feature_name = module_data.get('feature_name')
        success = save_enhanced_module_to_registry(
            feature_name,
            source_code,  # Use selected source code
            original_object_path,
            migration_name,
            cloud_category  # Use cloud_category from request
        )

        if not success:
            print(f"❌ Failed to save to registry file system - ID: {request.module_id}")
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "Registry file save failed",
                    "message": "Failed to save module to registry file system",
                    "module_id": request.module_id,
                    "registry_path": registry_path
                }
            )

        print(f"✅ Successfully moved {source_description.lower()} to registry - ID: {request.module_id}")
        return {
            "success": True,
            "message": f"Module {action} in registry successfully",
            "module_id": request.module_id,
            "feature_name": feature_name,
            "source_selection": request.source_selection,
            "source_description": source_description,
            "cloud_category": cloud_category,
            "migration_name": migration_name,
            "registry_path": registry_path,
            "action": action,
            "updated_fields": {
                "registry_module_code": f"Updated with {source_description.lower()}",
                "is_inregistry": True,
                "registry_path": registry_path
            }
        }

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        print(f"❌ Unexpected error moving module to registry: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Internal server error",
                "message": f"Unexpected error: {str(e)}",
                "module_id": request.module_id
            }
        )


@router.post("/merge-to-qbook")
async def merge_module_to_qbook(request: MergeToQBookRequest):
    """
    Merge registry module code back to original QBook file system.

    This completes the enhancement lifecycle: Original QBook → Enhancement → Registry → QBook (Updated)

    Requirements:
    - Module must be in registry (is_inregistry = true)
    - Module must not be already merged (is_merged = false)
    - Registry module code must exist and not be empty
    - cloud_category must be provided (local/cloud)

    Operations:
    - Updates database: sets is_merged = true
    - Updates QBook file system: overwrites original with registry code (encrypted)
    """
    try:
        print(f"🔀 Merging module to QBook - Module ID: {request.module_id}, Cloud: {request.cloud_category}")

        # 1. Validate cloud_category
        if request.cloud_category.lower() not in ["local", "cloud"]:
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "Invalid cloud category",
                    "message": "Cloud category must be 'local' or 'cloud'",
                    "provided_cloud_category": request.cloud_category
                }
            )

        # 2. Get current module data
        module_data = get_stage2_conversion_module_by_id(
            db_data=get_qbook_db_config(),
            module_id=request.module_id
        )

        if not module_data:
            print(f"❌ Module not found with ID: {request.module_id}")
            raise HTTPException(
                status_code=404,
                detail={
                    "error": "Module not found",
                    "message": f"No module found with ID: {request.module_id}",
                    "module_id": request.module_id
                }
            )

        # 3. Validate module is in registry
        if not module_data.get('is_inregistry', False):
            print(f"❌ Module not in registry - ID: {request.module_id}")
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "Module not in registry",
                    "message": "Module must be in registry before merging to QBook",
                    "module_id": request.module_id,
                    "is_inregistry": module_data.get('is_inregistry', False)
                }
            )

        # 4. Validate module is not already merged
        if module_data.get('is_merged', False):
            print(f"❌ Module already merged - ID: {request.module_id}")
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "Module already merged",
                    "message": "Module has already been merged to QBook",
                    "module_id": request.module_id,
                    "is_merged": module_data.get('is_merged', False)
                }
            )

        # 5. Validate registry module code exists
        registry_module_code = module_data.get('registry_module_code', '')
        if not registry_module_code or registry_module_code.strip() == "":
            print(f"❌ No registry module code available - ID: {request.module_id}")
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "No registry module code",
                    "message": "No registry module code available to merge to QBook",
                    "module_id": request.module_id
                }
            )

        # 6. Determine QBook target path
        original_object_path = module_data.get('original_object_path')
        migration_name = module_data.get('migration_name')

        # Get QBook base path based on cloud category
        if request.cloud_category.lower() == 'local':
            qbook_base_path = Config.Qbook_Local_Path
            print(f"🏠 Using Local QBook path: {qbook_base_path}")
        else:
            qbook_base_path = Config.Qbook_Path
            print(f"☁️ Using Cloud QBook path: {qbook_base_path}")

        # Construct QBook target path
        qbook_target_path = os.path.join(
            qbook_base_path,
            'Conversion_Modules',
            migration_name,
            original_object_path
        )

        print(f"🎯 QBook target path: {qbook_target_path}")

        # 7. Update database - set is_merged = true
        print(f"💾 Updating database to mark as merged...")
        updated_module_id = update_stage2_conversion_module_status(
            db_data=get_qbook_db_config(),
            module_id=request.module_id,
            is_merged=True,  # Mark as merged
            reviewer_name=request.reviewer_name,
            reviewer_comments=f"Merged to QBook: {request.comments}"
        )

        if not updated_module_id:
            print(f"❌ Failed to update database - ID: {request.module_id}")
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "Database update failed",
                    "message": "Failed to update module merge status in database",
                    "module_id": request.module_id
                }
            )

        # 8. Save encrypted module to QBook file system
        print(f"🔐 Encrypting and saving registry code to QBook...")
        success = encrypt_and_save_module(
            registry_module_code,
            qbook_target_path,
            migration_name
        )

        if not success:
            print(f"❌ Failed to save to QBook file system - ID: {request.module_id}")
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "QBook file save failed",
                    "message": "Failed to save module to QBook file system",
                    "module_id": request.module_id,
                    "qbook_target_path": qbook_target_path
                }
            )

        feature_name = module_data.get('feature_name')
        registry_path = module_data.get('registry_path', '')

        print(f"✅ Successfully merged module to QBook - ID: {request.module_id}")
        return {
            "success": True,
            "message": "Module merged to QBook successfully",
            "module_id": request.module_id,
            "feature_name": feature_name,
            "cloud_category": request.cloud_category,
            "migration_name": migration_name,
            "registry_source": registry_path,
            "qbook_target": qbook_target_path,
            "updated_fields": {
                "is_merged": True
            }
        }

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        print(f"❌ Unexpected error merging module to QBook: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Internal server error",
                "message": f"Unexpected error: {str(e)}",
                "module_id": request.module_id
            }
        )


# ================================ Stage 2 Metadata APIs ================================

@router.get("/metadata/schemas")
async def get_schemas_by_process_type(process_type: str, migration_name: str):
    """
    Get distinct schema names for a given process type and migration name.
    Used for cascading dropdown in UI.
    """
    try:
        print(f"🔍 Getting schema names for process_type: {process_type}, migration_name: {migration_name}")

        # Get schema names from database
        schema_names = get_schema_names_by_process_type(
            db_data=get_qbook_db_config(),
            process_type=process_type,
            migration_name=migration_name
        )

        print(f"✅ Found {len(schema_names)} schema names for process_type: {process_type}")

        return {
            "message": "Schema names retrieved successfully",
            "status": "success",
            "process_type": process_type,
            "migration_name": migration_name,
            "schemas": schema_names,
            "count": len(schema_names)
        }

    except Exception as e:
        print(f"❌ Error getting schema names: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to retrieve schema names",
                "message": str(e),
                "process_type": process_type,
                "migration_name": migration_name
            }
        )


@router.get("/metadata/object-types")
async def get_object_types_from_schema(process_type: str, migration_name: str, schema_name: str):
    """
    Get distinct object types for a given process type, migration name, and schema name.
    Used for cascading dropdown in UI.
    """
    try:
        print(f"🔍 Getting object types for process_type: {process_type}, migration_name: {migration_name}, schema_name: {schema_name}")

        # Get object types from database
        object_types = get_object_types_by_schema(
            db_data=get_qbook_db_config(),
            process_type=process_type,
            migration_name=migration_name,
            schema_name=schema_name
        )

        print(f"✅ Found {len(object_types)} object types for schema: {schema_name}")

        return {
            "message": "Object types retrieved successfully",
            "status": "success",
            "process_type": process_type,
            "migration_name": migration_name,
            "schema_name": schema_name,
            "object_types": object_types,
            "count": len(object_types)
        }

    except Exception as e:
        print(f"❌ Error getting object types: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to retrieve object types",
                "message": str(e),
                "process_type": process_type,
                "migration_name": migration_name,
                "schema_name": schema_name
            }
        )


@router.get("/metadata/object-names")
async def get_object_names_from_type(process_type: str, migration_name: str, schema_name: str, object_type: str):
    """
    Get distinct object names for a given process type, migration name, schema name, and object type.
    Used for cascading dropdown in UI.
    """
    try:
        print(f"🔍 Getting object names for process_type: {process_type}, migration_name: {migration_name}, schema_name: {schema_name}, object_type: {object_type}")

        # Get object names from database
        object_names = get_object_names_by_type(
            db_data=get_qbook_db_config(),
            process_type=process_type,
            migration_name=migration_name,
            schema_name=schema_name,
            object_type=object_type
        )

        print(f"✅ Found {len(object_names)} object names for object_type: {object_type}")

        return {
            "message": "Object names retrieved successfully",
            "status": "success",
            "process_type": process_type,
            "migration_name": migration_name,
            "schema_name": schema_name,
            "object_type": object_type,
            "object_names": object_names,
            "count": len(object_names)
        }

    except Exception as e:
        print(f"❌ Error getting object names: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to retrieve object names",
                "message": str(e),
                "process_type": process_type,
                "migration_name": migration_name,
                "schema_name": schema_name,
                "object_type": object_type
            }
        )


@router.get("/metadata/target-statements")
async def get_target_statements_for_dropdown(process_type: str, migration_name: str, schema_name: str, object_type: str, object_name: str):
    """
    Get distinct target statement numbers for dropdown in Agent Review section.
    Used to populate the statement selection dropdown.
    """
    try:
        print(f"🔍 Getting target statements for: {process_type}/{migration_name}/{schema_name}/{object_type}/{object_name}")

        # Get target statement numbers from database
        target_statements = get_target_statement_numbers(
            db_data=get_qbook_db_config(),
            process_type=process_type,
            migration_name=migration_name,
            schema_name=schema_name,
            object_type=object_type,
            object_name=object_name
        )

        print(f"✅ Found {len(target_statements)} target statements")

        return {
            "message": "Target statements retrieved successfully",
            "status": "success",
            "process_type": process_type,
            "migration_name": migration_name,
            "schema_name": schema_name,
            "object_type": object_type,
            "object_name": object_name,
            "target_statements": target_statements,
            "count": len(target_statements)
        }

    except Exception as e:
        print(f"❌ Error getting target statements: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to retrieve target statements",
                "message": str(e),
                "process_type": process_type,
                "migration_name": migration_name,
                "schema_name": schema_name,
                "object_type": object_type,
                "object_name": object_name
            }
        )


@router.get("/metadata/statement-details")
async def get_statement_details_with_modules(
    target_statement_number: int,
    process_type: str,
    migration_name: str,
    schema_name: str,
    object_type: str,
    object_name: str
):
    """
    Get complete statement data with all corresponding module attempts.
    Used when a specific statement is selected from the dropdown.
    """
    try:
        print(f"🔍 Getting statement details for statement #{target_statement_number}")

        # Get statement and module data from database
        result = get_statement_with_modules(
            db_data=get_qbook_db_config(),
            target_statement_number=target_statement_number,
            process_type=process_type,
            migration_name=migration_name,
            schema_name=schema_name,
            object_type=object_type,
            object_name=object_name
        )
        if result is None:
            print(f"❌ No statement found for statement #{target_statement_number}")
            raise HTTPException(
                status_code=404,
                detail={
                    "error": "Statement not found",
                    "message": f"No statement found with number {target_statement_number}",
                    "target_statement_number": target_statement_number
                }
            )

        print(f"✅ Found statement with {len(result.get('attempts', {}))} attempts")

        return {
            "message": "Statement and module data retrieved successfully",
            "status": "success",
            "target_statement_number": target_statement_number,
            **result  # Spread the combined object (statement fields + attempts structure)
        }

    except Exception as e:
        print(f"❌ Error getting statement details: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to retrieve statement details",
                "message": str(e),
                "target_statement_number": target_statement_number
            }
        )


@router.get("/download-file")
async def download_stage2_file(
    migration_name: str,
    schema_name: str,
    object_type: str,
    object_name: str,
    cloud_category: str = "Local"
):
    """
    Download Stage 2 files with standard naming pattern.

    Automatically finds and downloads files matching pattern: {schema_name}_{object_name}_Stage2.*
    File path pattern: {qbook_path}/Stage1_Metadata/{migration_name}/{schema_name}/{object_type}/{object_name}/

    Examples of files that will be found:
    - "AICCTEST_P_GETISSEROPOSITIVE_Stage2.xlsx"
    - "AICCTEST_P_GETISSEROPOSITIVE_Stage2.sql"
    - "AICCTEST_P_GETISSEROPOSITIVE_Stage2.txt"
    - "AICCTEST_P_GETISSEROPOSITIVE_Stage2.json"

    Behavior:
    - Single file found: Downloads the file directly
    - Multiple files found: Downloads all files as a ZIP archive
    - No files found: Returns 404 error
    """
    try:
        print(f"📥 Looking for Stage2 files for: {migration_name}/{schema_name}/{object_type}/{object_name}")
        print(f"🌍 Environment: {cloud_category}")

        # Determine Qbook path based on cloud_category (following existing pattern)
        if cloud_category.lower() == "local":
            qbook_path = Config.Qbook_Local_Path
            print(f"🏠 Using QBook Local path: {qbook_path}")
        else:
            qbook_path = Config.Qbook_Path
            print(f"☁️ Using QBook Cloud path: {qbook_path}")

        # Build file directory path
        file_directory = os.path.join(
            qbook_path,
            'Stage1_Metadata',
            migration_name,
            schema_name,
            object_type,
            object_name
        )

        # Check if directory exists
        if not os.path.exists(file_directory):
            print(f"❌ Directory not found: {file_directory}")
            raise HTTPException(
                status_code=404,
                detail={
                    "error": "Directory not found",
                    "message": f"Metadata directory not found for {schema_name}.{object_name}",
                    "expected_directory": file_directory
                }
            )

        # Find files matching the pattern: {schema_name}_{object_name}_Stage2.*
        file_pattern = f"{schema_name}_{object_name}_Stage2.*"
        search_pattern = os.path.join(file_directory, file_pattern)
        matching_files = glob.glob(search_pattern)

        print(f"📁 Searching for pattern: {file_pattern}")
        print(f"🔍 Found {len(matching_files)} matching files: {matching_files}")

        if not matching_files:
            raise HTTPException(
                status_code=404,
                detail={
                    "error": "No Stage2 files found",
                    "message": f"No files matching pattern '{file_pattern}' found for {schema_name}.{object_name}",
                    "search_directory": file_directory,
                    "pattern": file_pattern,
                    "migration_name": migration_name,
                    "schema_name": schema_name,
                    "object_type": object_type,
                    "object_name": object_name
                }
            )

        # Handle single file vs multiple files
        if len(matching_files) == 1:
            # Single file - download directly
            file_path = matching_files[0]
            filename = os.path.basename(file_path)
            print(f"📁 Single file found: {file_path}")

            # Determine media type based on file extension
            file_extension = os.path.splitext(filename)[1].lower()
            media_type_mapping = {
                '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                '.xls': 'application/vnd.ms-excel',
                '.sql': 'text/plain',
                '.txt': 'text/plain',
                '.py': 'text/plain',
                '.json': 'application/json',
                '.csv': 'text/csv',
                '.pdf': 'application/pdf',
                '.zip': 'application/zip'
            }

            media_type = media_type_mapping.get(file_extension, 'application/octet-stream')

            print(f"✅ Single file found, preparing download...")

            # Return single file for download
            return FileResponse(
                path=file_path,
                filename=filename,
                media_type=media_type,
                headers={
                    "Content-Disposition": f"attachment; filename={filename}"
                }
            )

        else:
            # Multiple files - create ZIP archive
            print(f"📦 Multiple files found ({len(matching_files)}), creating ZIP archive...")

            # Create temporary ZIP file
            zip_filename = f"{schema_name}_{object_name}_Stage2_Files.zip"
            temp_zip_path = tempfile.mktemp(suffix='.zip')

            try:
                with zipfile.ZipFile(temp_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for file_path in matching_files:
                        file_name = os.path.basename(file_path)
                        zipf.write(file_path, file_name)
                        print(f"📄 Added to ZIP: {file_name}")

                print(f"✅ ZIP archive created with {len(matching_files)} files")

                # Return ZIP file for download
                return FileResponse(
                    path=temp_zip_path,
                    filename=zip_filename,
                    media_type="application/zip",
                    headers={
                        "Content-Disposition": f"attachment; filename={zip_filename}"
                    }
                )

            except Exception as zip_error:
                # Clean up temp file if ZIP creation fails
                if os.path.exists(temp_zip_path):
                    os.remove(temp_zip_path)
                raise HTTPException(
                    status_code=500,
                    detail={
                        "error": "Failed to create ZIP archive",
                        "message": str(zip_error),
                        "files_count": len(matching_files)
                    }
                )



    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        print(f"❌ Error downloading file: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to download file",
                "message": str(e),
                "migration_name": migration_name,
                "schema_name": schema_name,
                "object_type": object_type,
                "object_name": object_name
            }
        )


# ================================ Request Management APIs ================================

@router.get("/requests/recent")
async def get_recent_request_records(limit: int = 50):
    """
    Get recent request records from the requests table.
    Returns the last N records ordered by created_dt descending.

    Parameters:
    - limit: Number of records to retrieve (default: 50, max: 200)

    Response includes:
    - request_id: Unique identifier for the request
    - process_type: Type of process (qmigrator, qbook, etc.)
    - migration_name: Name of the migration
    - schema_name: Schema name
    - object_type: Type of object being processed
    - object_name: Name of the object
    - status: Current status (Started, Completed, Error)
    - error: Error message if status is Error
    - created_dt: When the request was created
    - updated_dt: When the request was last updated
    """
    try:
        # Validate limit parameter
        if limit <= 0:
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "Invalid limit parameter",
                    "message": "Limit must be a positive integer",
                    "provided_limit": limit
                }
            )

        if limit > 200:
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "Limit too high",
                    "message": "Maximum limit is 200 records",
                    "provided_limit": limit,
                    "max_limit": 200
                }
            )

        print(f"🔍 Getting last {limit} request records...")

        # Get recent requests from database
        requests_data = get_recent_requests(
            db_data=get_qbook_db_config(),
            limit=limit
        )

        if requests_data is None:
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "Database query failed",
                    "message": "Failed to retrieve request records from database"
                }
            )

        print(f"✅ Retrieved {len(requests_data)} request records")

        # Group requests by status for summary
        status_summary = {}
        for request in requests_data:
            status = request.get('status', 'Unknown')
            status_summary[status] = status_summary.get(status, 0) + 1

        return {
            "message": "Recent request records retrieved successfully",
            "status": "success",
            "limit": limit,
            "total_records": len(requests_data),
            "status_summary": status_summary,
            "requests": requests_data
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        print(f"❌ Error getting recent requests: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to retrieve recent requests",
                "message": str(e),
                "limit": limit
            }
        )
