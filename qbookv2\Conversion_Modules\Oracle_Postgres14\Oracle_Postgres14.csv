Migration_Name,Feature_Name,Keywords,Predecessor,Estimations,Automation_Percentage,Object_Path
Oracle_Postgres14,Xmltype,xmltype,No Predecessor,2,100,Common/Statement/Pre
Oracle_Postgres14,Cursor,cursor & is,Post_procedure,3,70,Procedure/Post
Oracle_Postgres14,Add_month,add_months(*),No Predecessor,5,60,Common/Statement/Pre
Oracle_Postgres14,Bitand,bitand(%),No Predecessor,2,100,Common/Statement/Pre
Oracle_Postgres14,Instr,instr,No Predecessor,5,100,Common/Statement/Pre
Oracle_Postgres14,Nvl2,nvl2*,No Predecessor,2,70,Common/Statement/Pre
Oracle_Postgres14,Systimestamp,systimestamp,No Predecessor,2,90,Common/Statement/Pre
Oracle_Postgres14,Offset,*offset* & rows,No Predecessor,3,80,Procedure/Statement/Pre
Oracle_Postgres14,Zero_divide,Exception&zero_divide,No Predecessor,4,80,Common/Pre
Oracle_Postgres14,Rowid,rowid,No Predecessor,2,100,Common/Statement/Pre
Oracle_Postgres14,Rawtohex,*rowtohex(%),No Predecessor,3,90,Common/Statement/Pre
Oracle_Postgres14,To_char,to_char&from_tz,No Predecessor,5,70,Common/Statement/Pre
Oracle_Postgres14,Last_day,last_day,No Predecessor,5,90,Common/Statement/Pre
Oracle_Postgres14,Months_between,months_between,No Predecessor,5,90,Common/Statement/Pre
Oracle_Postgres14,To_number,to_number(%),No Predecessor,3,90,Common/Statement/Pre
Oracle_Postgres14,Ascii,ascii(%),No Predecessor,1,100,Procedure/Statement/Pre
Oracle_Postgres14,Dblink,Null,No Predecessor,0,0,Dblink
Oracle_Postgres14,Exit_found,exit*found,No Predecessor,3,70,Common/Statement/Pre
Oracle_Postgres14,Listagg,listagg*,No Predecessor,3,80,Common/Statement/Pre
Oracle_Postgres14,Program,Null,No Predecessor,0,0,Program
Oracle_Postgres14,Owa_cookie,owa_cookie.,No Predecessor,0,0,Common/Statement/Pre
Oracle_Postgres14,Utl_tcp,utl_tcp.,No Predecessor,0,0,Common/Statement/Pre
Oracle_Postgres14,Owa_util,OWA_UTIL,No Predecessor,0,0,Common/Statement/Pre
Oracle_Postgres14,Decode,decode*,No Predecessor,3,80,Common/Statement/Pre
Oracle_Postgres14,Job,Null,No Predecessor,0,0,Job
Oracle_Postgres14,Schedule,Null,No Predecessor,0,0,Schedule
Oracle_Postgres14,Any,any,No Predecessor,1,100,Common/Statement/Pre
Oracle_Postgres14,Sequence,no cache|no cycle|no order|create sequence,No Predecessor,2,100,Sequence/Pre
Oracle_Postgres14,Datatype,Null,No Predecessor,0,0,Datatype
Oracle_Postgres14,Trunc,trunc(%),Datatypes,1,80,Common/Statement/Pre
Oracle_Postgres14,Java_language_wrapper,java.lang.,No Predecessor,0,0,Common/Statement/Pre
Oracle_Postgres14,Column,Null,No Predecessor,0,0,Column
Oracle_Postgres14,Materialized_view,REFRESH Fast| REFRESH FORCE|REFRESH Completed|ON commit|ON DEMAND|create materialized view,No Predecessor,15,30,Materialized_View/Pre
Oracle_Postgres14,Insert_brackets_handling,insert into & values((,No Predecessor,2,70,Common/Statement/Pre
Oracle_Postgres14,Temporary_table,create table,No Predecessor,0,0,Temporary_Table/Pre
Oracle_Postgres14,Checkconstraint,alter table & check,No Predecessor,1,100,Check_Constraint/Pre
Oracle_Postgres14,Commit,commit,No Predecessor,1,100,Common/Statement/Pre
Oracle_Postgres14,Self_is_null,SELF_IS_NULL,No Predecessor,1,100,Common/Statement/Pre
Oracle_Postgres14,Nvl,nvl*,No Predecessor,2,100,Common/Statement/Pre
Oracle_Postgres14,Primarykey,alter table & PRIMARY & CONSTRAINT,No Predecessor,1,100,Primary_Key/Pre
Oracle_Postgres14,Sysdate,sysdate,Trunc,2,100,Common/Statement/Pre
Oracle_Postgres14,Sys_context,sys_context,No Predecessor,3,90,Common/Statement/Pre
Oracle_Postgres14,Sqlrowcount,rowcount|notfound,Post_procedure,3,80,Procedure/Post
Oracle_Postgres14,Nextval,Nextval,No Predecessor,3,70,Common/Statement/Pre
Oracle_Postgres14,Exception_commenting,PRAGMA|Exception,Post_procedure,3,80,Procedure/Post
Oracle_Postgres14,Dup_val_on_index,dup_val_on_index&Exception,No Predecessor,5,80,Common/Pre
Oracle_Postgres14,Date_format,to_char&RRRR|to_date&RRRR,No Predecessor,5,50,Procedure/Post
Oracle_Postgres14,Dbms_utility,DBMS_UTILITY,No Predecessor,5,70,Common/Statement/Pre
Oracle_Postgres14,Dbms_utility_comma,dbms_utility&comma_to_table,No Predecessor,5,70,Common/Statement/Pre
Oracle_Postgres14,Existsnode,existsnode,No Predecessor,4,60,Common/Statement/Pre
Oracle_Postgres14,Synonym,create or replace synonym|create synonym,No Predecessor,5,0,Synonym/Pre
Oracle_Postgres14,Datatypes,number|varchar2|nvarchar|float|sysdate|create or replace|create type,No Predecessor,2,100,Type/Pre
Oracle_Postgres14,Regexp_count,select & regexp_count,No Predecessor,5,0,Common/Statement/Pre
Oracle_Postgres14,Dbms_utility,dbms_utility,No Predecessor,5,70,Procedure/Post
Oracle_Postgres14,Execute_dynamic_variable,open&text|varchar,Post_procedure,3,60,Procedure/Post
Oracle_Postgres14,Date_format,to_char&RRRR|to_date&RRRR,Post_functions,5,50,Function/Post
Oracle_Postgres14,Minus,minus,No Predecessor,2,100,Common/Pre
Oracle_Postgres14,Record,for&in&loop,Post_functions,3,80,Function/Post
Oracle_Postgres14,Copynocopy,copy|nocopy,No Predecessor,2,100,Common/Pre
Oracle_Postgres14,Index,create unique index|create index,No Predecessor,10,50,Index/Pre
Oracle_Postgres14,Update_alias,update|set,No Predecessor,2,100,Common/Statement/Pre
Oracle_Postgres14,Notnullconstraint,alter table & NOT NULL,No Predecessor,1,100,Not_Null_Constraint/Pre
Oracle_Postgres14,Post_functions,create or replace|create function,No Predecessor,50,100,Function/Post
Oracle_Postgres14,Datatypes,varchar2|number|nvarchar2|clob|date|out sys_refcursor|inout|long|integer|sys_refcursor|sys_refcursor|rowid|string|raw,No Predecessor,30,100,Common/Pre
Oracle_Postgres14,Dbms_output,dbms_output,No Predecessor,5,70,Common/Pre
Oracle_Postgres14,Merge,Merge,No Predecessor,7,70,Common/Pre
Oracle_Postgres14,Defaultconstraint,alter table & DEFAULT,No Predecessor,1,100,Default_Constraint/Pre
Oracle_Postgres14,Record,for&in&loop,Post_procedure,3,80,Procedure/Post
Oracle_Postgres14,Post_procedure,create or replace|create or alter|create,No Predecessor,50,100,Procedure/Post
Oracle_Postgres14,View,create,No Predecessor,5,80,View/Pre
Oracle_Postgres14,Dbms_utility_format_error,FORMAT_ERROR_BACKTRACE,Dbms_output,5,70,Common/Pre
Oracle_Postgres14,Strict,select & into,Post_procedure,2,100,Procedure/Post
Oracle_Postgres14,Dual,Dual,No Predecessor,1,80,Common/Pre
Oracle_Postgres14,Currval,Currval,No Predecessor,3,100,Common/Statement/Pre
Oracle_Postgres14,Time_intervals,24*60*60|24*3600|86400|24*60|1440,No Predecessor,3,40,Common/Pre
Oracle_Postgres14,Getclobval,getclobval,No Predecessor,3,100,Common/Statement/Pre
Oracle_Postgres14,Null_handling,select & '|insert & ',No Predecessor,2,40,Common/Statement/Pre
Oracle_Postgres14,Pls_integer,pls_integer,No Predecessor,2,100,Common/Statement/Pre
Oracle_Postgres14,Sqlcode,SQLCODE&SQLERRM,No Predecessor,5,100,Common/Pre
Oracle_Postgres14,Strict,select & into,Post_functions,2,100,Function/Post
Oracle_Postgres14,Execute_dynamic_variable,varchar|text,No Predecessor,3,60,Function/Post
Oracle_Postgres14,Exception_commenting,PRAGMA|Exception,Post_functions,2,80,Function/Post
Oracle_Postgres14,Exception_handling,exception,No Predecessor,5,40,Common/Pre
Oracle_Postgres14,Pivot,pivot,No Predecessor,7,0,Common/Statement/Pre
Oracle_Postgres14,Regexp_substr,Regexp_substr,No Predecessor,3,0,Common/Statement/Pre
Oracle_Postgres14,Dbms_ldap,dbms_ldap.,No Predecessor,0,0,Common/Statement/Pre
Oracle_Postgres14,Case_when,select unnest,Extractvalue,5,70,Common/Pre
Oracle_Postgres14,Rollback_to_savepoint,rollback,No Predecessor,2,50,Common/Statement/Pre
Oracle_Postgres14,Raisenotice,raise notice,No Predecessor,5,70,Common/Statement/Pre
Oracle_Postgres14,Default_null,:=Null|Default|Default null|:= Null|varchar2 :=|varchar2:=|number :=|number:=|numeric :=|varchar:=|TYPE :=|numeric:=|varchar :=|TYPE:=|:=true|:=false|:= true|:= false|:= sysdate,No Predecessor,2,100,Common/Pre
Oracle_Postgres14,Raise_application_error,raise_application_error,No Predecessor,5,70,Common/Statement/Pre
Oracle_Postgres14,End_case,end case,No Predecessor,2,0,Common/Statement/Pre
Oracle_Postgres14,Xml_sequence,table,Case_when,5,70,Common/Pre
Oracle_Postgres14,Dbms_session,dbms_session,No Predecessor,2,70,Common/Pre
Oracle_Postgres14,Trunc,trunc,No Predecessor,1,80,View/Statement/Pre
Oracle_Postgres14,Connect_by_level,select * from * connect by * level,No Predecessor,5,30,Common/Statement/Pre
Oracle_Postgres14,Regexp_like,Regexp_like,No Predecessor,3,0,Common/Statement/Pre
Oracle_Postgres14,Unpivot,unpivot,No Predecessor,7,0,Common/Statement/Pre
Oracle_Postgres14,Send_mail,send_mail,No Predecessor,0,0,Common/Statement/Pre
Oracle_Postgres14,Htp,htp.,No Predecessor,0,0,Common/Statement/Pre
Oracle_Postgres14,Dbms_lob,dbms_lob.getlength,No Predecessor,2,100,Common/Post
Oracle_Postgres14,Inouts,in out|out,No Predecessor,2,100,Common/Pre
Oracle_Postgres14,Xmltype,xmltype,No Predecessor,1,90,Common/Pre
Oracle_Postgres14,Underscorecursor,cursor|type,Type_record,3,100,Common/Post
Oracle_Postgres14,Datatypes,number|varchar2|guid|sys_guid|sysdate|systimestamp|clob|blob|long|varchar|bfile|nvarchar|float|long raw|binary_double|binary_float|nvarchar2,No Predecessor,30,100,Partition/Post
Oracle_Postgres14,Datatypes,number|varchar2|guid|sys_guid|sysdate|systimestamp|clob|blob|long|varchar|bfile|nvarchar|float|long raw|binary_double|binary_float|nvarchar2|date|timestamp|char,No Predecessor,30,100,Table/Pre
Oracle_Postgres14,Partition,create table|partition by range|partition by list|partition by hash,No Predecessor,5,30,Partition/Pre
Oracle_Postgres14,Type_refcursor,type&ref cursor&is,No Predecessor,2,100,Common/Post
Oracle_Postgres14,Mod,Mod,No Predecessor,2,90,Common/Statement/Pre
Oracle_Postgres14,Dbms_sleep_lock,dbms_lock,No Predecessor,2,80,Common/Statement/Pre
Oracle_Postgres14,Type_record,Type&is&record,No Predecessor,2,100,Common/Post
Oracle_Postgres14,Sub_query_alias,select&from&( & ),No Predecessor,3,70,Common/Post
Oracle_Postgres14,Binary_integer,binary_integer,No Predecessor,1,100,Common/Pre
Oracle_Postgres14,Cursor,cursor & is,No Predecessor,3,70,Function/Post
Oracle_Postgres14,Type_scenario2,TYPE|ROWTYPE,No Predecessor,3,85,Common/Post
Oracle_Postgres14,Rownum,rownum,No Predecessor,3,100,Common/Pre
Oracle_Postgres14,Extend,extend,Type_scenario2,2,90,Common/Post
Oracle_Postgres14,Foreignkey,alter table &foreign & references,No Predecessor,2,85,Foreign_Key/Pre
Oracle_Postgres14,Uniqueconstraint,alter table & unique,No Predecessor,1,100,Unique_Constraint/Pre
Oracle_Postgres14,Unique,unique,No Predecessor,2,100,Common/Pre
Oracle_Postgres14,Value_error,value_error&EXCEPTION,No Predecessor,1,100,Common/Statement/Pre
Oracle_Postgres14,Type_scenario1,TYPE & IS & TABLE,No Predecessor,2,100,Common/Post
Oracle_Postgres14,Insert_alias,insert,No Predecessor,3,60,Common/Post
Oracle_Postgres14,Type_tableof,Type&table of,No Predecessor,2,70,Common/Post
Oracle_Postgres14,Connect_by_level,connect by&REGEXP_SUBSTR,No Predecessor,5,30,Common/Pre
Oracle_Postgres14,Connect_by_prior,start&connect,No Predecessor,5,50,Common/Post
Oracle_Postgres14,Sys_context,sys_context,No Predecessor,1,100,Common/Post
Oracle_Postgres14,Join,select&from&where,No Predecessor,3,60,Common/Statement/Pre
Oracle_Postgres14,Having_issue,select&having*group,No Predecessor,1,90,Common/Statement/Post
Oracle_Postgres14,Raise_select_unnest,raise&notice&select&unnest,No Predecessor,1,100,Common/Statement/Post
Oracle_Postgres14,Nvl,nvl,No Predecessor,1,100,Common/Post
Oracle_Postgres14,Tunc_date,trunc,No Predecessor,1,100,Common/Post
Oracle_Postgres14,Convert_authid_statement,CURRENT_USER&AUTHID,No Predecessor,1,100,Procedure/Post
Oracle_Postgres14,Convert_authid_statement,CURRENT_USER&AUTHID,No Predecessor,1,100,Common/Post
Oracle_Postgres14,Sysdate,sysdate,No Predecessor,1,100,Common/Post
Oracle_Postgres14,Condition_dml_triggers,returns&Trigger,Post_functions,2,90,Function/Post
Oracle_Postgres14,Datatypes,varchar2|number|nvarchar2|clob|date|out sys_refcursor|inout|long|integer|sys_refcursor|minus|sys_refcursor|rowid|string|raw|sysdate|nvl|trunc,No Predecessor,1,100,View/Pre
Oracle_Postgres14,Exception_variable_commenting,EXCEPTION&WHEN,No Predecessor,1,100,Common/Post
Oracle_Postgres14,Bulk_collect,bulk collect into *,No Predecessor,2,0,Common/Post
Oracle_Postgres14,Utl_file,utl_file.,No Predecessor,2,0,Common/Post
Oracle_Postgres14,Goto,goto,No Predecessor,2,20,Common/Post
Oracle_Postgres14,Plsql_unit,plsql_unit,No Predecessor,1,100,Common/Post
Oracle_Postgres14,Input_parameters_issue,record,No Predecessor,1,100,Common/Post
Oracle_Postgres14,Convert_from_string_to_array,from_string_to_array,No Predecessor,1,100,Common/Statement/Pre
Oracle_Postgres14,To_char,to_char,No Predecessor,1,90,View/Pre
Oracle_Postgres14,Rownum,rownum,No Predecessor,2,90,View/Pre
Oracle_Postgres14,Package_name_exception,when others then & raise,No Predecessor,1,100,Common/Post
Oracle_Postgres14,Trunc_to_date,trunc&to_date,No Predecessor,1,70,View/Pre
Oracle_Postgres14,Raise_application_error,raise_application_error,No Predecessor,5,70,Common/Post
Oracle_Postgres14,Extractvalue,extractvalue|extract,No Predecessor,4,60,Common/Pre
Oracle_Postgres14,Connect_by_rownum,CONNECT&BY&rownum,No Predecessor,1,70,Common/Post
